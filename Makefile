# Loom Project Makefile
# Supports dev, test, staging, and production environments

.PHONY: dev-start dev-stop test test-backend test-frontend lint lint-backend lint-frontend build build-backend build-frontend clean install install-backend install-frontend

# Development commands
dev-start:
	@if [ ! -f backend/.env ]; then \
		cp backend/.env.example backend/.env; \
	fi
	docker-compose up --build -d

dev-stop:
	docker-compose down

dev-logs:
	docker-compose logs -f

dev-restart:
	docker-compose restart

# Testing commands
test: test-backend test-frontend

test-backend:
	docker-compose exec backend python -m pytest tests/ -v --cov=server --cov-report=html --cov-report=term

test-frontend:
	docker-compose --profile dev run --rm frontend-dev npm test -- --run

test-frontend-watch:
	docker-compose --profile dev run --rm frontend-dev npm test

test-frontend-coverage:
	docker-compose --profile dev run --rm frontend-dev npm run test:coverage

# Linting commands
lint: lint-backend lint-frontend

lint-backend:
	@echo "🔍 Running backend linting..."
	docker-compose exec -u root backend python -m black server/ tests/ scripts/ --check
	docker-compose exec -u root backend python -m flake8 server/ tests/ scripts/
	docker-compose exec -u root backend python -m mypy server/

lint-frontend:
	docker-compose exec frontend npm run lint

# Build commands
build: build-backend build-frontend

build-backend:
	docker-compose build backend

build-frontend:
	docker-compose build frontend

# Installation commands
install: install-backend install-frontend

install-backend:
	docker-compose build backend

install-frontend:
	docker-compose build frontend

# Utility commands
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	rm -rf backend/.coverage 2>/dev/null || true
	rm -rf backend/htmlcov 2>/dev/null || true
	@if [ -d "frontend/dist" ]; then rm -rf frontend/dist; fi
	@if [ -d "frontend/build" ]; then rm -rf frontend/build; fi

# Format code (auto-fix)
format-backend:
	@echo "🎨 Formatting backend code..."
	docker-compose exec -u root backend python -m black server/ tests/ scripts/

# Fix linting issues
lint-fix-backend:
	@echo "🔧 Auto-fixing backend linting issues..."
	docker-compose exec -u root backend python -m black server/ tests/ scripts/
	@echo "✅ Code formatted with black"

# Database commands
db-migrate:
	docker-compose exec backend alembic upgrade head

db-reset:
	docker-compose exec backend alembic downgrade base
	docker-compose exec backend alembic upgrade head

db-revision:
	docker-compose exec backend alembic revision --autogenerate -m "$(MESSAGE)"

db-current:
	docker-compose exec backend alembic current

db-history:
	docker-compose exec backend alembic history

db-show:
	docker-compose exec postgres psql -U loom_user -d loom -c "\dt"

# Development data seeding
seed-data:
	docker-compose exec backend python scripts/seed_professional_datasets.py

seed-data-reset:
	docker-compose exec backend python scripts/seed_professional_datasets.py --reset

# Large-scale performance data seeding
seed-large-data:
	docker-compose exec backend python scripts/seed_large_dataset.py

seed-large-data-reset:
	docker-compose exec backend python scripts/seed_large_dataset.py --reset

seed-large-data-custom:
	@echo "Usage: make seed-large-data-custom POINTS=500000"
	@if [ -z "$(POINTS)" ]; then \
		echo "Error: POINTS parameter is required"; \
		echo "Example: make seed-large-data-custom POINTS=500000"; \
		exit 1; \
	fi
	docker-compose exec backend python scripts/seed_large_dataset.py --points $(POINTS)

# Docker-specific commands
docker-build:
	docker-compose build

docker-clean:
	docker-compose down -v --remove-orphans
	docker system prune -f

docker-shell-backend:
	docker-compose exec backend /bin/bash

docker-shell-frontend:
	docker-compose exec frontend /bin/sh

docker-shell-db:
	docker-compose exec postgres psql -U loom_user -d loom