"""Configuration settings for the Loom application."""

from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    app_name: str = Field(default="Loom API")
    environment: str = Field(default="development")
    debug: bool = Field(default=False)

    host: str = Field(default="0.0.0.0")
    port: int = Field(default=8000)
    workers: int = Field(default=1)

    database_url: Optional[str] = Field(default=None)
    db_host: str = Field(default="localhost")
    db_port: int = Field(default=5432)
    db_name: str = Field(default="loom")
    db_user: str = Field(default="loom_user")
    db_password: str = Field(default="")

    secret_key: str = Field(default="your-secret-key-change-in-production")
    algorithm: str = Field(default="HS256")
    access_token_expire_minutes: int = Field(default=30)

    cors_origins: str = Field(default="*")

    max_upload_size: int = Field(default=100 * 1024 * 1024)
    upload_dir: str = Field(default="uploads")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    @property
    def database_url_complete(self) -> str:
        if self.database_url:
            return self.database_url
        return f"postgresql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"

    @property
    def is_development(self) -> bool:
        return self.environment.lower() in ["dev", "development"]

    @property
    def is_production(self) -> bool:
        return self.environment.lower() in ["prod", "production"]

    @property
    def is_testing(self) -> bool:
        return self.environment.lower() in ["test", "testing"]

    @property
    def cors_origins_list(self) -> list[str]:
        if self.cors_origins == "*":
            return ["*"]
        return [
            origin.strip() for origin in self.cors_origins.split(",") if origin.strip()
        ]


settings = Settings()


def get_settings() -> Settings:
    return settings
