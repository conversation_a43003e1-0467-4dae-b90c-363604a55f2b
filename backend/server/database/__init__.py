"""Database package for Loom backend."""

from .tables import User, Image, Point, Embedding, ReducedEmbedding
from .connection import engine, SessionLocal, get_db

# Note: Query modules will be updated in next phase
# from .queries import users, images, points, embeddings, reduced_embeddings
from .utils import (
    current_timestamp_ms,
    timestamp_to_datetime,
    datetime_to_timestamp,
    format_timestamp,
    parse_timestamp,
)

__all__ = [
    "engine",
    "SessionLocal",
    "get_db",
    "User",
    "Image",
    "Point",
    "Embedding",
    "ReducedEmbedding",
    # Query modules will be added back in next phase
    "current_timestamp_ms",
    "timestamp_to_datetime",
    "datetime_to_timestamp",
    "format_timestamp",
    "parse_timestamp",
]
