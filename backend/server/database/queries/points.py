"""Query functions for Point table operations."""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from ..tables import Point, Image, Embedding, ReducedEmbedding
from ..utils import current_timestamp_ms


def create_point(
    db: Session,
    image_id: int,
    x: float,
    y: float,
    radius: float,
) -> Point:
    """Create a new point record."""
    timestamp = current_timestamp_ms()
    
    point = Point(
        image_id=image_id,
        x=x,
        y=y,
        radius=radius,
        created_at=timestamp,
        updated_at=timestamp,
    )
    
    db.add(point)
    db.commit()
    db.refresh(point)
    return point


def get_point_by_id(db: Session, point_id: int) -> Optional[Point]:
    """Get a point by its ID."""
    return db.query(Point).filter(Point.id == point_id).first()


def get_points_by_image(
    db: Session,
    image_id: int,
    skip: int = 0,
    limit: int = 1000,
    filters: Optional[Dict[str, Any]] = None,
) -> List[Point]:
    """Get points for an image with optional filtering."""
    query = db.query(Point).filter(Point.image_id == image_id)
    
    if filters:
        # Coordinate filtering
        if "x_min" in filters:
            query = query.filter(Point.x >= filters["x_min"])
        if "x_max" in filters:
            query = query.filter(Point.x <= filters["x_max"])
        if "y_min" in filters:
            query = query.filter(Point.y >= filters["y_min"])
        if "y_max" in filters:
            query = query.filter(Point.y <= filters["y_max"])
        
        # Radius filtering
        if "radius_min" in filters:
            query = query.filter(Point.radius >= filters["radius_min"])
        if "radius_max" in filters:
            query = query.filter(Point.radius <= filters["radius_max"])
    
    return query.offset(skip).limit(limit).all()


def get_points_by_user_with_filters(
    db: Session,
    user_id: int,
    skip: int = 0,
    limit: int = 50000,  # Default sampling limit for performance
    image_filters: Optional[Dict[str, Any]] = None,
    point_filters: Optional[Dict[str, Any]] = None,
) -> List[Dict[str, Any]]:
    """Get points for a user with filtering on both image and point properties."""
    query = (
        db.query(Point, Image)
        .join(Image, Point.image_id == Image.id)
        .filter(Image.user_id == user_id)
    )
    
    # Apply image filters
    if image_filters:
        if "captured_after" in image_filters:
            query = query.filter(Image.captured_at >= image_filters["captured_after"])
        if "captured_before" in image_filters:
            query = query.filter(Image.captured_at <= image_filters["captured_before"])
        if "lat_min" in image_filters:
            query = query.filter(Image.latitude >= image_filters["lat_min"])
        if "lat_max" in image_filters:
            query = query.filter(Image.latitude <= image_filters["lat_max"])
        if "lng_min" in image_filters:
            query = query.filter(Image.longitude >= image_filters["lng_min"])
        if "lng_max" in image_filters:
            query = query.filter(Image.longitude <= image_filters["lng_max"])
        if "robot_id" in image_filters:
            if isinstance(image_filters["robot_id"], list):
                query = query.filter(Image.robot_id.in_(image_filters["robot_id"]))
            else:
                query = query.filter(Image.robot_id == image_filters["robot_id"])
        if "height_min" in image_filters:
            query = query.filter(Image.height >= image_filters["height_min"])
        if "height_max" in image_filters:
            query = query.filter(Image.height <= image_filters["height_max"])
        if "width_min" in image_filters:
            query = query.filter(Image.width >= image_filters["width_min"])
        if "width_max" in image_filters:
            query = query.filter(Image.width <= image_filters["width_max"])
    
    # Apply point filters
    if point_filters:
        if "x_min" in point_filters:
            query = query.filter(Point.x >= point_filters["x_min"])
        if "x_max" in point_filters:
            query = query.filter(Point.x <= point_filters["x_max"])
        if "y_min" in point_filters:
            query = query.filter(Point.y >= point_filters["y_min"])
        if "y_max" in point_filters:
            query = query.filter(Point.y <= point_filters["y_max"])
        if "radius_min" in point_filters:
            query = query.filter(Point.radius >= point_filters["radius_min"])
        if "radius_max" in point_filters:
            query = query.filter(Point.radius <= point_filters["radius_max"])
    
    results = query.offset(skip).limit(limit).all()
    
    # Convert to dictionaries with combined point and image data
    return [
        {
            "id": point.id,
            "image_id": point.image_id,
            "x": point.x,
            "y": point.y,
            "radius": point.radius,
            "created_at": point.created_at,
            "updated_at": point.updated_at,
            # Image metadata
            "image_captured_at": image.captured_at,
            "image_latitude": image.latitude,
            "image_longitude": image.longitude,
            "image_height": image.height,
            "image_width": image.width,
            "image_robot_id": image.robot_id,
        }
        for point, image in results
    ]


def get_point_count_by_image(db: Session, image_id: int) -> int:
    """Get count of points for an image."""
    return db.query(func.count(Point.id)).filter(Point.image_id == image_id).scalar()


def get_point_count_by_user_with_filters(
    db: Session,
    user_id: int,
    image_filters: Optional[Dict[str, Any]] = None,
    point_filters: Optional[Dict[str, Any]] = None,
) -> int:
    """Get count of points for a user with filtering."""
    query = (
        db.query(func.count(Point.id))
        .join(Image, Point.image_id == Image.id)
        .filter(Image.user_id == user_id)
    )
    
    # Apply same filters as get_points_by_user_with_filters
    if image_filters:
        if "captured_after" in image_filters:
            query = query.filter(Image.captured_at >= image_filters["captured_after"])
        if "captured_before" in image_filters:
            query = query.filter(Image.captured_at <= image_filters["captured_before"])
        if "lat_min" in image_filters:
            query = query.filter(Image.latitude >= image_filters["lat_min"])
        if "lat_max" in image_filters:
            query = query.filter(Image.latitude <= image_filters["lat_max"])
        if "lng_min" in image_filters:
            query = query.filter(Image.longitude >= image_filters["lng_min"])
        if "lng_max" in image_filters:
            query = query.filter(Image.longitude <= image_filters["lng_max"])
        if "robot_id" in image_filters:
            if isinstance(image_filters["robot_id"], list):
                query = query.filter(Image.robot_id.in_(image_filters["robot_id"]))
            else:
                query = query.filter(Image.robot_id == image_filters["robot_id"])
        if "height_min" in image_filters:
            query = query.filter(Image.height >= image_filters["height_min"])
        if "height_max" in image_filters:
            query = query.filter(Image.height <= image_filters["height_max"])
        if "width_min" in image_filters:
            query = query.filter(Image.width >= image_filters["width_min"])
        if "width_max" in image_filters:
            query = query.filter(Image.width <= image_filters["width_max"])
    
    if point_filters:
        if "x_min" in point_filters:
            query = query.filter(Point.x >= point_filters["x_min"])
        if "x_max" in point_filters:
            query = query.filter(Point.x <= point_filters["x_max"])
        if "y_min" in point_filters:
            query = query.filter(Point.y >= point_filters["y_min"])
        if "y_max" in point_filters:
            query = query.filter(Point.y <= point_filters["y_max"])
        if "radius_min" in point_filters:
            query = query.filter(Point.radius >= point_filters["radius_min"])
        if "radius_max" in point_filters:
            query = query.filter(Point.radius <= point_filters["radius_max"])
    
    return query.scalar()


def delete_point(db: Session, point_id: int) -> bool:
    """Delete a point and all its related data."""
    point = get_point_by_id(db, point_id)
    if not point:
        return False
    
    db.delete(point)
    db.commit()
    return True


def create_points_batch(
    db: Session,
    image_id: int,
    points_data: List[Dict[str, float]],
) -> List[Point]:
    """Create multiple points for an image in a batch."""
    timestamp = current_timestamp_ms()
    
    points = []
    for point_data in points_data:
        point = Point(
            image_id=image_id,
            x=point_data["x"],
            y=point_data["y"],
            radius=point_data["radius"],
            created_at=timestamp,
            updated_at=timestamp,
        )
        points.append(point)
    
    db.add_all(points)
    db.commit()
    
    for point in points:
        db.refresh(point)
    
    return points
