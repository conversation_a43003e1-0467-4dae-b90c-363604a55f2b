"""Dataset-related database queries."""

from sqlalchemy.orm import Session
from typing import List, Optional
from ..tables import Dataset


class DatasetQueries:
    """Database queries for Dataset table."""
    
    @staticmethod
    def get_by_id(db: Session, dataset_id: int) -> Optional[Dataset]:
        """Get dataset by ID."""
        return db.query(Dataset).filter(Dataset.id == dataset_id).first()
    
    @staticmethod
    def get_by_name(db: Session, name: str, owner_id: Optional[int] = None) -> Optional[Dataset]:
        """Get dataset by name, optionally filtered by owner."""
        query = db.query(Dataset).filter(Dataset.name == name)
        if owner_id is not None:
            query = query.filter(Dataset.owner_id == owner_id)
        return query.first()
    
    @staticmethod
    def get_by_owner(db: Session, owner_id: int, skip: int = 0, limit: int = 100) -> List[Dataset]:
        """Get all datasets owned by a user with pagination."""
        return (db.query(Dataset)
                .filter(Dataset.owner_id == owner_id)
                .offset(skip)
                .limit(limit)
                .all())
    
    @staticmethod
    def get_all(db: Session, skip: int = 0, limit: int = 100) -> List[Dataset]:
        """Get all datasets with pagination."""
        return db.query(Dataset).offset(skip).limit(limit).all()
    
    @staticmethod
    def count_all(db: Session) -> int:
        """Get total count of datasets."""
        return db.query(Dataset).count()
    
    @staticmethod
    def count_by_owner(db: Session, owner_id: int) -> int:
        """Get total count of datasets by owner."""
        return db.query(Dataset).filter(Dataset.owner_id == owner_id).count()
    
    @staticmethod
    def get_by_status(db: Session, status: str, skip: int = 0, limit: int = 100) -> List[Dataset]:
        """Get datasets by status with pagination."""
        return (db.query(Dataset)
                .filter(Dataset.status == status)
                .offset(skip)
                .limit(limit)
                .all())
    
    @staticmethod
    def create(db: Session, name: str, owner_id: int, description: Optional[str] = None,
               total_points: int = 0, embedding_dimension: Optional[int] = None,
               file_path: Optional[str] = None, file_size: Optional[int] = None,
               status: str = "uploaded") -> Dataset:
        """Create a new dataset."""
        dataset = Dataset(
            name=name,
            description=description,
            owner_id=owner_id,
            total_points=total_points,
            embedding_dimension=embedding_dimension,
            file_path=file_path,
            file_size=file_size,
            status=status
        )
        db.add(dataset)
        db.commit()
        db.refresh(dataset)
        return dataset
    
    @staticmethod
    def update(db: Session, dataset_id: int, **kwargs) -> Optional[Dataset]:
        """Update dataset by ID."""
        dataset = db.query(Dataset).filter(Dataset.id == dataset_id).first()
        if dataset:
            for key, value in kwargs.items():
                if hasattr(dataset, key):
                    setattr(dataset, key, value)
            db.commit()
            db.refresh(dataset)
        return dataset
    
    @staticmethod
    def delete(db: Session, dataset_id: int) -> bool:
        """Delete dataset by ID."""
        dataset = db.query(Dataset).filter(Dataset.id == dataset_id).first()
        if dataset:
            db.delete(dataset)
            db.commit()
            return True
        return False
