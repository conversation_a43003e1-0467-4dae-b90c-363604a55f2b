"""Embedding-related database queries."""

from sqlalchemy.orm import Session
from sqlalchemy import func, text
from typing import List, Optional, NamedTuple
from ..tables import Embedding, DataPoint


class EmbeddingWithDataPoint(NamedTuple):
    """Named tuple for embedding data with datapoint information."""

    id: int
    embedding_type: str
    x: Optional[float]
    y: Optional[float]
    z: Optional[float]
    dimensions: int
    label: Optional[str]
    data_metadata: Optional[dict]
    original_index: Optional[int]


def get_by_datapoint(db: Session, datapoint_id: int) -> List[Embedding]:
    """Get all embeddings for a datapoint."""
    return db.query(Embedding).filter(Embedding.datapoint_id == datapoint_id).all()


def get_by_dataset_and_type(
    db: Session,
    dataset_id: int,
    embedding_type: str,
    skip: int = 0,
    limit: Optional[int] = None,
) -> List[Embedding]:
    """Get embeddings by dataset and type with pagination."""
    query = (
        db.query(Embedding)
        .join(DataPoint)
        .filter(
            DataPoint.dataset_id == dataset_id,
            Embedding.embedding_type == embedding_type,
        )
        .offset(skip)
    )
    if limit is not None:
        query = query.limit(limit)
    return query.all()


def count_by_dataset_and_type(db: Session, dataset_id: int, embedding_type: str) -> int:
    """Get total count of embeddings for a dataset and type."""
    return (
        db.query(Embedding)
        .join(DataPoint)
        .filter(
            DataPoint.dataset_id == dataset_id,
            Embedding.embedding_type == embedding_type,
        )
        .count()
    )


def get_embeddings_with_datapoint_info(
    db: Session,
    dataset_id: int,
    embedding_type: Optional[str] = None,
    limit: Optional[int] = None,
) -> List[EmbeddingWithDataPoint]:
    """Get embeddings with datapoint information for visualization."""
    query = (
        db.query(
            Embedding.id,
            Embedding.embedding_type,
            Embedding.x,
            Embedding.y,
            Embedding.z,
            Embedding.dimensions,
            DataPoint.label,
            DataPoint.data_metadata,
            DataPoint.original_index,
        )
        .join(DataPoint, Embedding.datapoint_id == DataPoint.id)
        .filter(DataPoint.dataset_id == dataset_id)
    )

    # Filter by embedding type if specified
    if embedding_type:
        query = query.filter(Embedding.embedding_type == embedding_type)

    # Apply limit if specified
    if limit:
        query = query.limit(limit)

    results = query.all()
    return [EmbeddingWithDataPoint(*row) for row in results]


def get_embeddings_sample(
    db: Session,
    dataset_id: int,
    embedding_type: str,
    sample_size: int,
    sampling_method: str = "random",
    seed: Optional[int] = None,
) -> List[Embedding]:
    """Get a sample of embeddings for a dataset with various sampling methods."""
    # Base query
    query = (
        db.query(Embedding)
        .join(DataPoint)
        .filter(
            DataPoint.dataset_id == dataset_id,
            Embedding.embedding_type == embedding_type,
        )
    )

    # Apply sampling method
    if sampling_method == "random":
        if seed is not None:
            # Set seed for reproducible random sampling
            db.execute(text(f"SELECT setseed({seed / 2147483647.0})"))
        query = query.order_by(func.random())
    elif sampling_method == "systematic":
        # Get total count for systematic sampling
        total_count = query.count()
        if total_count > sample_size:
            step = total_count // sample_size
            query = query.filter(DataPoint.id % step == 0)
    elif sampling_method == "first_n":
        query = query.order_by(DataPoint.id)
    else:
        # Default to first_n for unknown methods
        query = query.order_by(DataPoint.id)

    return query.limit(sample_size).all()


def get_embeddings_sample_with_datapoint_info(
    db: Session,
    dataset_id: int,
    embedding_type: str,
    sample_size: int,
    sampling_method: str = "random",
    seed: Optional[int] = None,
) -> List[EmbeddingWithDataPoint]:
    """Get a sample of embeddings with datapoint information for visualization."""
    # Base query with joined data
    query = (
        db.query(
            Embedding.id,
            Embedding.embedding_type,
            Embedding.x,
            Embedding.y,
            Embedding.z,
            Embedding.dimensions,
            DataPoint.label,
            DataPoint.data_metadata,
            DataPoint.original_index,
        )
        .join(DataPoint, Embedding.datapoint_id == DataPoint.id)
        .filter(
            DataPoint.dataset_id == dataset_id,
            Embedding.embedding_type == embedding_type,
        )
    )

    # Apply sampling method
    if sampling_method == "random":
        if seed is not None:
            # Set seed for reproducible random sampling
            db.execute(text(f"SELECT setseed({seed / 2147483647.0})"))
        query = query.order_by(func.random())
    elif sampling_method == "systematic":
        # Get total count for systematic sampling
        total_count = query.count()
        if total_count > sample_size:
            step = total_count // sample_size
            query = query.filter(DataPoint.id % step == 0)
    elif sampling_method == "first_n":
        query = query.order_by(DataPoint.id)
    else:
        # Default to first_n for unknown methods
        query = query.order_by(DataPoint.id)

    results = query.limit(sample_size).all()
    return [EmbeddingWithDataPoint(*row) for row in results]


def create(
    db: Session,
    datapoint_id: int,
    embedding_type: str,
    dimensions: int,
    vector: List[float],
    method_params: Optional[dict] = None,
    x: Optional[float] = None,
    y: Optional[float] = None,
    z: Optional[float] = None,
) -> Embedding:
    """Create a new embedding."""
    embedding = Embedding(
        datapoint_id=datapoint_id,
        embedding_type=embedding_type,
        dimensions=dimensions,
        vector=vector,
        method_params=method_params,
        x=x,
        y=y,
        z=z,
    )
    db.add(embedding)
    db.commit()
    db.refresh(embedding)
    return embedding


def get_embedding_statistics_by_dataset(db: Session, dataset_id: int):
    """Get embedding statistics grouped by type for a dataset."""
    return (
        db.query(
            Embedding.embedding_type,
            func.count(Embedding.id).label("count"),
            func.min(Embedding.x).label("min_x"),
            func.max(Embedding.x).label("max_x"),
            func.min(Embedding.y).label("min_y"),
            func.max(Embedding.y).label("max_y"),
            func.min(Embedding.z).label("min_z"),
            func.max(Embedding.z).label("max_z"),
        )
        .join(DataPoint, Embedding.datapoint_id == DataPoint.id)
        .filter(DataPoint.dataset_id == dataset_id)
        .group_by(Embedding.embedding_type)
        .all()
    )
