"""Query functions for Embedding table operations."""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from ..tables import Embedding, Point, Image, ReducedEmbedding
from ..utils import current_timestamp_ms


def create_embedding(
    db: Session,
    point_id: int,
    model_id: str,
    vector: List[float],
    dimensions: int,
) -> Embedding:
    """Create a new embedding record."""
    timestamp = current_timestamp_ms()

    embedding = Embedding(
        point_id=point_id,
        model_id=model_id,
        vector=vector,
        dimensions=dimensions,
        created_at=timestamp,
        updated_at=timestamp,
    )

    db.add(embedding)
    db.commit()
    db.refresh(embedding)
    return embedding


def get_embedding_by_id(db: Session, embedding_id: int) -> Optional[Embedding]:
    """Get an embedding by its ID."""
    return db.query(Embedding).filter(Embedding.id == embedding_id).first()


def get_embeddings_by_point(
    db: Session,
    point_id: int,
    model_id: Optional[str] = None,
) -> List[Embedding]:
    """Get embeddings for a point, optionally filtered by model."""
    query = db.query(Embedding).filter(Embedding.point_id == point_id)

    if model_id:
        query = query.filter(Embedding.model_id == model_id)

    return query.all()


def delete_embedding(db: Session, embedding_id: int) -> bool:
    """Delete an embedding and all its related data."""
    embedding = get_embedding_by_id(db, embedding_id)
    if not embedding:
        return False

    db.delete(embedding)
    db.commit()
    return True


def get_unique_model_ids(db: Session, user_id: int) -> List[str]:
    """Get list of unique model IDs for a user."""
    result = (
        db.query(Embedding.model_id)
        .join(Point, Embedding.point_id == Point.id)
        .join(Image, Point.image_id == Image.id)
        .filter(Image.user_id == user_id)
        .distinct()
        .all()
    )
    return [row[0] for row in result]


def create_embeddings_batch(
    db: Session,
    embeddings_data: List[Dict[str, Any]],
) -> List[Embedding]:
    """Create multiple embeddings in a batch."""
    timestamp = current_timestamp_ms()

    embeddings = []
    for embedding_data in embeddings_data:
        embedding = Embedding(
            point_id=embedding_data["point_id"],
            model_id=embedding_data["model_id"],
            vector=embedding_data["vector"],
            dimensions=embedding_data["dimensions"],
            created_at=timestamp,
            updated_at=timestamp,
        )
        embeddings.append(embedding)

    db.add_all(embeddings)
    db.commit()

    for embedding in embeddings:
        db.refresh(embedding)

    return embeddings



