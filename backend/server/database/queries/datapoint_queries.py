"""DataPoint-related database queries."""

from sqlalchemy.orm import Session
from typing import List, Optional
from ..tables import DataPoint


class DataPointQueries:
    """Database queries for DataPoint table."""
    
    @staticmethod
    def get_by_id(db: Session, datapoint_id: int) -> Optional[DataPoint]:
        """Get datapoint by ID."""
        return db.query(DataPoint).filter(DataPoint.id == datapoint_id).first()
    
    @staticmethod
    def get_by_dataset(db: Session, dataset_id: int, skip: int = 0, limit: Optional[int] = None) -> List[DataPoint]:
        """Get all datapoints for a dataset with pagination."""
        query = db.query(DataPoint).filter(DataPoint.dataset_id == dataset_id).offset(skip)
        if limit is not None:
            query = query.limit(limit)
        return query.all()
    
    @staticmethod
    def get_by_dataset_and_label(db: Session, dataset_id: int, label: str) -> List[DataPoint]:
        """Get datapoints by dataset and label."""
        return (db.query(DataPoint)
                .filter(DataPoint.dataset_id == dataset_id, DataPoint.label == label)
                .all())
    
    @staticmethod
    def get_by_original_index(db: Session, dataset_id: int, original_index: int) -> Optional[DataPoint]:
        """Get datapoint by dataset and original index."""
        return (db.query(DataPoint)
                .filter(DataPoint.dataset_id == dataset_id, 
                       DataPoint.original_index == original_index)
                .first())
    
    @staticmethod
    def count_by_dataset(db: Session, dataset_id: int) -> int:
        """Get total count of datapoints for a dataset."""
        return db.query(DataPoint).filter(DataPoint.dataset_id == dataset_id).count()
    
    @staticmethod
    def get_labels_by_dataset(db: Session, dataset_id: int) -> List[str]:
        """Get unique labels for a dataset."""
        result = (db.query(DataPoint.label)
                 .filter(DataPoint.dataset_id == dataset_id, DataPoint.label.isnot(None))
                 .distinct()
                 .all())
        return [row[0] for row in result]
    
    @staticmethod
    def create(db: Session, dataset_id: int, label: Optional[str] = None,
               data_metadata: Optional[dict] = None, original_index: Optional[int] = None) -> DataPoint:
        """Create a new datapoint."""
        datapoint = DataPoint(
            dataset_id=dataset_id,
            label=label,
            data_metadata=data_metadata,
            original_index=original_index
        )
        db.add(datapoint)
        db.commit()
        db.refresh(datapoint)
        return datapoint
    
    @staticmethod
    def create_batch(db: Session, datapoints_data: List[dict]) -> List[DataPoint]:
        """Create multiple datapoints in batch."""
        datapoints = [DataPoint(**data) for data in datapoints_data]
        db.add_all(datapoints)
        db.commit()
        for datapoint in datapoints:
            db.refresh(datapoint)
        return datapoints
    
    @staticmethod
    def update(db: Session, datapoint_id: int, **kwargs) -> Optional[DataPoint]:
        """Update datapoint by ID."""
        datapoint = db.query(DataPoint).filter(DataPoint.id == datapoint_id).first()
        if datapoint:
            for key, value in kwargs.items():
                if hasattr(datapoint, key):
                    setattr(datapoint, key, value)
            db.commit()
            db.refresh(datapoint)
        return datapoint
    
    @staticmethod
    def delete(db: Session, datapoint_id: int) -> bool:
        """Delete datapoint by ID."""
        datapoint = db.query(DataPoint).filter(DataPoint.id == datapoint_id).first()
        if datapoint:
            db.delete(datapoint)
            db.commit()
            return True
        return False
    
    @staticmethod
    def delete_by_dataset(db: Session, dataset_id: int) -> int:
        """Delete all datapoints for a dataset. Returns count of deleted records."""
        count = db.query(DataPoint).filter(DataPoint.dataset_id == dataset_id).count()
        db.query(DataPoint).filter(DataPoint.dataset_id == dataset_id).delete()
        db.commit()
        return count
