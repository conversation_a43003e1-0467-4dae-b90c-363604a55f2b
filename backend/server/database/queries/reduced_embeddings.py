"""Query functions for ReducedEmbedding table operations."""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from ..tables import ReducedEmbedding, Embedding, Point, Image
from ..utils import current_timestamp_ms


def create_reduced_embedding(
    db: Session,
    embedding_id: int,
    method: str,
    x: float,
    y: float,
    z: Optional[float] = None,
    method_params: Optional[Dict[str, Any]] = None,
) -> ReducedEmbedding:
    """Create a new reduced embedding record."""
    timestamp = current_timestamp_ms()
    
    reduced_embedding = ReducedEmbedding(
        embedding_id=embedding_id,
        method=method,
        x=x,
        y=y,
        z=z,
        method_params=method_params,
        created_at=timestamp,
        updated_at=timestamp,
    )
    
    db.add(reduced_embedding)
    db.commit()
    db.refresh(reduced_embedding)
    return reduced_embedding


def get_reduced_embedding_by_id(db: Session, reduced_embedding_id: int) -> Optional[ReducedEmbedding]:
    """Get a reduced embedding by its ID."""
    return db.query(ReducedEmbedding).filter(ReducedEmbedding.id == reduced_embedding_id).first()


def get_reduced_embeddings_by_embedding(
    db: Session,
    embedding_id: int,
    method: Optional[str] = None,
) -> List[ReducedEmbedding]:
    """Get reduced embeddings for an embedding, optionally filtered by method."""
    query = db.query(ReducedEmbedding).filter(ReducedEmbedding.embedding_id == embedding_id)
    
    if method:
        query = query.filter(ReducedEmbedding.method == method)
    
    return query.all()


def get_reduced_embeddings_for_visualization(
    db: Session,
    user_id: int,
    method: str,
    model_id: Optional[str] = None,
    image_filters: Optional[Dict[str, Any]] = None,
    point_filters: Optional[Dict[str, Any]] = None,
    skip: int = 0,
    limit: int = 50000,
) -> List[Dict[str, Any]]:
    """Get reduced embeddings for 3D visualization with filtering."""
    query = (
        db.query(ReducedEmbedding, Embedding, Point, Image)
        .join(Embedding, ReducedEmbedding.embedding_id == Embedding.id)
        .join(Point, Embedding.point_id == Point.id)
        .join(Image, Point.image_id == Image.id)
        .filter(
            Image.user_id == user_id,
            ReducedEmbedding.method == method
        )
    )
    
    if model_id:
        query = query.filter(Embedding.model_id == model_id)
    
    # Apply image filters
    if image_filters:
        if "captured_after" in image_filters:
            query = query.filter(Image.captured_at >= image_filters["captured_after"])
        if "captured_before" in image_filters:
            query = query.filter(Image.captured_at <= image_filters["captured_before"])
        if "lat_min" in image_filters:
            query = query.filter(Image.latitude >= image_filters["lat_min"])
        if "lat_max" in image_filters:
            query = query.filter(Image.latitude <= image_filters["lat_max"])
        if "lng_min" in image_filters:
            query = query.filter(Image.longitude >= image_filters["lng_min"])
        if "lng_max" in image_filters:
            query = query.filter(Image.longitude <= image_filters["lng_max"])
        if "robot_id" in image_filters:
            if isinstance(image_filters["robot_id"], list):
                query = query.filter(Image.robot_id.in_(image_filters["robot_id"]))
            else:
                query = query.filter(Image.robot_id == image_filters["robot_id"])
        if "height_min" in image_filters:
            query = query.filter(Image.height >= image_filters["height_min"])
        if "height_max" in image_filters:
            query = query.filter(Image.height <= image_filters["height_max"])
        if "width_min" in image_filters:
            query = query.filter(Image.width >= image_filters["width_min"])
        if "width_max" in image_filters:
            query = query.filter(Image.width <= image_filters["width_max"])
    
    # Apply point filters
    if point_filters:
        if "x_min" in point_filters:
            query = query.filter(Point.x >= point_filters["x_min"])
        if "x_max" in point_filters:
            query = query.filter(Point.x <= point_filters["x_max"])
        if "y_min" in point_filters:
            query = query.filter(Point.y >= point_filters["y_min"])
        if "y_max" in point_filters:
            query = query.filter(Point.y <= point_filters["y_max"])
        if "radius_min" in point_filters:
            query = query.filter(Point.radius >= point_filters["radius_min"])
        if "radius_max" in point_filters:
            query = query.filter(Point.radius <= point_filters["radius_max"])
    
    results = query.offset(skip).limit(limit).all()
    
    return [
        {
            "id": reduced_embedding.id,
            "embedding_id": reduced_embedding.embedding_id,
            "method": reduced_embedding.method,
            "x": reduced_embedding.x,
            "y": reduced_embedding.y,
            "z": reduced_embedding.z,
            "method_params": reduced_embedding.method_params,
            "created_at": reduced_embedding.created_at,
            "updated_at": reduced_embedding.updated_at,
            # Embedding data
            "model_id": embedding.model_id,
            "dimensions": embedding.dimensions,
            # Point data
            "point_id": point.id,
            "point_x": point.x,
            "point_y": point.y,
            "point_radius": point.radius,
            # Image data
            "image_id": image.id,
            "image_captured_at": image.captured_at,
            "image_latitude": image.latitude,
            "image_longitude": image.longitude,
            "image_height": image.height,
            "image_width": image.width,
            "image_robot_id": image.robot_id,
        }
        for reduced_embedding, embedding, point, image in results
    ]


def get_reduced_embedding_bounds(
    db: Session,
    user_id: int,
    method: str,
    model_id: Optional[str] = None,
) -> Dict[str, List[float]]:
    """Get the bounds (min/max) for reduced embeddings for visualization scaling."""
    query = (
        db.query(
            func.min(ReducedEmbedding.x).label("min_x"),
            func.max(ReducedEmbedding.x).label("max_x"),
            func.min(ReducedEmbedding.y).label("min_y"),
            func.max(ReducedEmbedding.y).label("max_y"),
            func.min(ReducedEmbedding.z).label("min_z"),
            func.max(ReducedEmbedding.z).label("max_z"),
        )
        .join(Embedding, ReducedEmbedding.embedding_id == Embedding.id)
        .join(Point, Embedding.point_id == Point.id)
        .join(Image, Point.image_id == Image.id)
        .filter(
            Image.user_id == user_id,
            ReducedEmbedding.method == method
        )
    )
    
    if model_id:
        query = query.filter(Embedding.model_id == model_id)
    
    result = query.first()
    
    if not result or result.min_x is None:
        return {"x": [0, 1], "y": [0, 1], "z": [0, 1]}
    
    return {
        "x": [result.min_x, result.max_x],
        "y": [result.min_y, result.max_y],
        "z": [result.min_z or 0, result.max_z or 1] if result.min_z is not None else [0, 1],
    }


def delete_reduced_embedding(db: Session, reduced_embedding_id: int) -> bool:
    """Delete a reduced embedding."""
    reduced_embedding = get_reduced_embedding_by_id(db, reduced_embedding_id)
    if not reduced_embedding:
        return False
    
    db.delete(reduced_embedding)
    db.commit()
    return True


def get_available_methods(db: Session, user_id: int) -> List[str]:
    """Get list of available reduction methods for a user."""
    result = (
        db.query(ReducedEmbedding.method)
        .join(Embedding, ReducedEmbedding.embedding_id == Embedding.id)
        .join(Point, Embedding.point_id == Point.id)
        .join(Image, Point.image_id == Image.id)
        .filter(Image.user_id == user_id)
        .distinct()
        .all()
    )
    return [row[0] for row in result]


def create_reduced_embeddings_batch(
    db: Session,
    reduced_embeddings_data: List[Dict[str, Any]],
) -> List[ReducedEmbedding]:
    """Create multiple reduced embeddings in a batch."""
    timestamp = current_timestamp_ms()
    
    reduced_embeddings = []
    for data in reduced_embeddings_data:
        reduced_embedding = ReducedEmbedding(
            embedding_id=data["embedding_id"],
            method=data["method"],
            x=data["x"],
            y=data["y"],
            z=data.get("z"),
            method_params=data.get("method_params"),
            created_at=timestamp,
            updated_at=timestamp,
        )
        reduced_embeddings.append(reduced_embedding)
    
    db.add_all(reduced_embeddings)
    db.commit()
    
    for reduced_embedding in reduced_embeddings:
        db.refresh(reduced_embedding)
    
    return reduced_embeddings
