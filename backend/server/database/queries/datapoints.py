"""DataPoint-related database queries."""

from sqlalchemy.orm import Session
from typing import List, Optional
from ..tables import DataPoint


def get_by_id(db: Session, datapoint_id: int) -> Optional[DataPoint]:
    """Get datapoint by ID."""
    return db.query(DataPoint).filter(DataPoint.id == datapoint_id).first()


def get_by_dataset(db: Session, dataset_id: int, skip: int = 0, limit: Optional[int] = None) -> List[DataPoint]:
    """Get all datapoints for a dataset with pagination."""
    query = db.query(DataPoint).filter(DataPoint.dataset_id == dataset_id).offset(skip)
    if limit is not None:
        query = query.limit(limit)
    return query.all()


def create(db: Session, dataset_id: int, label: Optional[str] = None,
           data_metadata: Optional[dict] = None, original_index: Optional[int] = None) -> DataPoint:
    """Create a new datapoint."""
    datapoint = DataPoint(
        dataset_id=dataset_id,
        label=label,
        data_metadata=data_metadata,
        original_index=original_index
    )
    db.add(datapoint)
    db.commit()
    db.refresh(datapoint)
    return datapoint
