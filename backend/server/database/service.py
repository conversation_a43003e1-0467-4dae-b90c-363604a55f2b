"""Database service functions for common operations."""

from sqlalchemy.orm import Session
from typing import List, Optional
from .tables import User, Dataset, DataPoint, Embedding
from .queries import UserQueries, DatasetQueries, DataPointQueries, EmbeddingQueries


class UserService:
    """Service class for user operations."""

    @staticmethod
    def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
        """Get user by ID."""
        return UserQueries.get_by_id(db, user_id)

    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[User]:
        """Get user by username."""
        return UserQueries.get_by_username(db, username)

    @staticmethod
    def get_user_by_email(db: Session, email: str) -> Optional[User]:
        """Get user by email."""
        return UserQueries.get_by_email(db, email)

    @staticmethod
    def create_user(db: Session, username: str, email: str, hashed_password: str,
                   full_name: Optional[str] = None) -> User:
        """Create a new user."""
        return UserQueries.create(db, username, email, hashed_password, full_name)


class DatasetService:
    """Service class for dataset operations."""

    @staticmethod
    def get_dataset_by_id(db: Session, dataset_id: int) -> Optional[Dataset]:
        """Get dataset by ID."""
        return DatasetQueries.get_by_id(db, dataset_id)

    @staticmethod
    def get_datasets_by_user(db: Session, user_id: int) -> List[Dataset]:
        """Get all datasets owned by a user."""
        return DatasetQueries.get_by_owner(db, user_id)

    @staticmethod
    def create_dataset(db: Session, name: str, owner_id: int,
                      description: Optional[str] = None) -> Dataset:
        """Create a new dataset."""
        return DatasetQueries.create(db, name, owner_id, description)


class DataPointService:
    """Service class for datapoint operations."""

    @staticmethod
    def get_datapoint_by_id(db: Session, datapoint_id: int) -> Optional[DataPoint]:
        """Get datapoint by ID."""
        return DataPointQueries.get_by_id(db, datapoint_id)

    @staticmethod
    def get_datapoints_by_dataset(db: Session, dataset_id: int) -> List[DataPoint]:
        """Get all datapoints for a dataset."""
        return DataPointQueries.get_by_dataset(db, dataset_id)

    @staticmethod
    def create_datapoint(db: Session, dataset_id: int, label: Optional[str] = None,
                        data_metadata: Optional[dict] = None,
                        original_index: Optional[int] = None) -> DataPoint:
        """Create a new datapoint."""
        return DataPointQueries.create(db, dataset_id, label, data_metadata, original_index)


class EmbeddingService:
    """Service class for embedding operations."""

    @staticmethod
    def get_embeddings_by_datapoint(db: Session, datapoint_id: int) -> List[Embedding]:
        """Get all embeddings for a datapoint."""
        return EmbeddingQueries.get_by_datapoint(db, datapoint_id)

    @staticmethod
    def get_embeddings_by_type(db: Session, dataset_id: int,
                              embedding_type: str) -> List[Embedding]:
        """Get embeddings by type for a dataset."""
        return EmbeddingQueries.get_by_dataset_and_type(db, dataset_id, embedding_type)
    
    @staticmethod
    def get_embeddings_sample(db: Session, dataset_id: int,
                             embedding_type: str, sample_size: int,
                             sampling_method: str = "random",
                             seed: Optional[int] = None) -> List[Embedding]:
        """Get a sample of embeddings for a dataset."""
        return EmbeddingQueries.get_embeddings_sample(
            db, dataset_id, embedding_type, sample_size, sampling_method, seed
        )

    @staticmethod
    def get_embedding_count(db: Session, dataset_id: int,
                           embedding_type: str) -> int:
        """Get total count of embeddings for a dataset and type."""
        return EmbeddingQueries.count_by_dataset_and_type(db, dataset_id, embedding_type)

    @staticmethod
    def create_embedding(db: Session, datapoint_id: int, embedding_type: str,
                        dimensions: int, vector: List[float],
                        method_params: Optional[dict] = None,
                        x: Optional[float] = None, y: Optional[float] = None,
                        z: Optional[float] = None) -> Embedding:
        """Create a new embedding."""
        return EmbeddingQueries.create(
            db, datapoint_id, embedding_type, dimensions, vector,
            method_params, x, y, z
        )
