"""Loom FastAPI Application Entry Point"""

from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
from typing import Dict, Any
from server.api.v1 import router as api_v1_router

app = FastAPI(
    title="Loom API",
    description="API for exploring large embedding datasets in 3D space",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(api_v1_router)


@app.get("/")
async def root() -> Dict[str, Any]:
    return {
        "message": "Welcome to Loom API",
        "version": "1.0.0",
        "description": "API for exploring large embedding datasets",
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/health")
async def health_check() -> Dict[str, Any]:
    return {
        "status": "healthy",
        "service": "loom-api",
        "environment": os.getenv("ENVIRONMENT", "development")
    }


@app.get("/api/v1/status")
async def api_status() -> Dict[str, Any]:
    return {
        "api_version": "v1",
        "status": "operational",
        "features": [
            "dataset_upload",
            "3d_visualization",
            "dimensionality_reduction",
            "filtering"
        ]
    }


# Let FastAPI handle HTTPExceptions with default format
# Only handle non-API 404s (like static files)
from starlette.exceptions import HTTPException as StarletteHTTPException

@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request, exc):
    # Only override for non-API routes
    if exc.status_code == 404 and not str(request.url.path).startswith("/api/"):
        return JSONResponse(
            status_code=404,
            content={"error": "Not found", "message": "The requested resource was not found"}
        )
    # For API routes, let FastAPI handle it with default format
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": str(exc.detail)}
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "message": "An unexpected error occurred"}
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
