"""API endpoints for image operations."""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ...database import get_db
from ...database.queries import users as user_queries, images as image_queries, points as point_queries, reduced_embeddings as reduced_embedding_queries
from .schemas import (
    ImageResponse,
    ImageListResponse,
    PointResponse,
    VisualizationResponse,
    VisualizationFilters,
    ImageFilters,
    PointFilters,
    EmbeddingPoint3D,
)

router = APIRouter()


@router.get("/", response_model=ImageListResponse)
def list_images(
    skip: int = Query(0, ge=0, description="Number of images to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of images to return"),
    # Image filters
    captured_after: Optional[int] = Query(None, description="Filter images captured after this timestamp"),
    captured_before: Optional[int] = Query(None, description="Filter images captured before this timestamp"),
    lat_min: Optional[float] = Query(None, description="Minimum latitude"),
    lat_max: Optional[float] = Query(None, description="Maximum latitude"),
    lng_min: Optional[float] = Query(None, description="Minimum longitude"),
    lng_max: Optional[float] = Query(None, description="Maximum longitude"),
    robot_id: Optional[str] = Query(None, description="Filter by robot ID"),
    height_min: Optional[int] = Query(None, description="Minimum image height"),
    height_max: Optional[int] = Query(None, description="Maximum image height"),
    width_min: Optional[int] = Query(None, description="Minimum image width"),
    width_max: Optional[int] = Query(None, description="Maximum image width"),
    db: Session = Depends(get_db),
) -> ImageListResponse:
    """List images with optional filtering."""
    # For now, using user_id = 3 (will be replaced with proper auth)
    user_id = 3
    
    # Build filters dict
    filters = {}
    if captured_after is not None:
        filters["captured_after"] = captured_after
    if captured_before is not None:
        filters["captured_before"] = captured_before
    if lat_min is not None:
        filters["lat_min"] = lat_min
    if lat_max is not None:
        filters["lat_max"] = lat_max
    if lng_min is not None:
        filters["lng_min"] = lng_min
    if lng_max is not None:
        filters["lng_max"] = lng_max
    if robot_id is not None:
        filters["robot_id"] = robot_id
    if height_min is not None:
        filters["height_min"] = height_min
    if height_max is not None:
        filters["height_max"] = height_max
    if width_min is not None:
        filters["width_min"] = width_min
    if width_max is not None:
        filters["width_max"] = width_max
    
    # Get images with filters
    images = image_queries.get_images_by_user(
        db=db,
        user_id=user_id,
        skip=skip,
        limit=limit,
        filters=filters if filters else None,
    )
    
    # Get total count
    total = image_queries.get_image_count_by_user(
        db=db,
        user_id=user_id,
        filters=filters if filters else None,
    )
    
    # Convert images to response format
    image_responses = []
    for image in images:
        # Get point count for this image
        point_count = point_queries.get_point_count_by_image(db=db, image_id=image.id)

        # Convert to dict and add point count
        image_dict = {
            "id": image.id,
            "user_id": image.user_id,
            "captured_at": image.captured_at,
            "latitude": image.latitude,
            "longitude": image.longitude,
            "height": image.height,
            "width": image.width,
            "robot_id": image.robot_id,
            "file_path": image.file_path,
            "file_size": image.file_size,
            "created_at": image.created_at,
            "updated_at": image.updated_at,
            "total_points": point_count,
        }
        image_responses.append(ImageResponse(**image_dict))
    
    return ImageListResponse(
        images=image_responses,
        total=total,
        skip=skip,
        limit=limit,
    )


@router.get("/{image_id}", response_model=ImageResponse)
def get_image(
    image_id: int,
    db: Session = Depends(get_db),
) -> ImageResponse:
    """Get a specific image by ID."""
    image_data = image_queries.get_image_with_points_count(db=db, image_id=image_id)
    if not image_data:
        raise HTTPException(status_code=404, detail="Image not found")
    
    return ImageResponse(**image_data)


@router.get("/{image_id}/points", response_model=List[PointResponse])
def get_image_points(
    image_id: int,
    skip: int = Query(0, ge=0, description="Number of points to skip"),
    limit: int = Query(1000, ge=1, le=10000, description="Maximum number of points to return"),
    # Point filters
    x_min: Optional[float] = Query(None, description="Minimum x coordinate"),
    x_max: Optional[float] = Query(None, description="Maximum x coordinate"),
    y_min: Optional[float] = Query(None, description="Minimum y coordinate"),
    y_max: Optional[float] = Query(None, description="Maximum y coordinate"),
    radius_min: Optional[float] = Query(None, description="Minimum radius"),
    radius_max: Optional[float] = Query(None, description="Maximum radius"),
    db: Session = Depends(get_db),
) -> List[PointResponse]:
    """Get points for a specific image."""
    # Check if image exists
    image = image_queries.get_image_by_id(db=db, image_id=image_id)
    if not image:
        raise HTTPException(status_code=404, detail="Image not found")
    
    # Build point filters
    filters = {}
    if x_min is not None:
        filters["x_min"] = x_min
    if x_max is not None:
        filters["x_max"] = x_max
    if y_min is not None:
        filters["y_min"] = y_min
    if y_max is not None:
        filters["y_max"] = y_max
    if radius_min is not None:
        filters["radius_min"] = radius_min
    if radius_max is not None:
        filters["radius_max"] = radius_max
    
    # Get points
    points = point_queries.get_points_by_image(
        db=db,
        image_id=image_id,
        skip=skip,
        limit=limit,
        filters=filters if filters else None,
    )
    
    return [PointResponse.model_validate(point) for point in points]


@router.get("/visualization/3d", response_model=VisualizationResponse)
def get_3d_visualization(
    method: str = Query(..., description="Reduction method (e.g., 'umap_3d')"),
    model_id: Optional[str] = Query(None, description="Filter by model ID"),
    sample_size: int = Query(50000, ge=1, le=100000, description="Maximum points to return"),
    # Image filters
    captured_after: Optional[int] = Query(None, description="Filter images captured after this timestamp"),
    captured_before: Optional[int] = Query(None, description="Filter images captured before this timestamp"),
    lat_min: Optional[float] = Query(None, description="Minimum latitude"),
    lat_max: Optional[float] = Query(None, description="Maximum latitude"),
    lng_min: Optional[float] = Query(None, description="Minimum longitude"),
    lng_max: Optional[float] = Query(None, description="Maximum longitude"),
    robot_id: Optional[str] = Query(None, description="Filter by robot ID"),
    # Point filters
    x_min: Optional[float] = Query(None, description="Minimum point x coordinate"),
    x_max: Optional[float] = Query(None, description="Maximum point x coordinate"),
    y_min: Optional[float] = Query(None, description="Minimum point y coordinate"),
    y_max: Optional[float] = Query(None, description="Maximum point y coordinate"),
    radius_min: Optional[float] = Query(None, description="Minimum point radius"),
    radius_max: Optional[float] = Query(None, description="Maximum point radius"),
    db: Session = Depends(get_db),
) -> VisualizationResponse:
    """Get 3D visualization data with filtering."""
    # For now, using user_id = 3 (will be replaced with proper auth)
    user_id = 3
    
    # Build image filters
    image_filters = {}
    if captured_after is not None:
        image_filters["captured_after"] = captured_after
    if captured_before is not None:
        image_filters["captured_before"] = captured_before
    if lat_min is not None:
        image_filters["lat_min"] = lat_min
    if lat_max is not None:
        image_filters["lat_max"] = lat_max
    if lng_min is not None:
        image_filters["lng_min"] = lng_min
    if lng_max is not None:
        image_filters["lng_max"] = lng_max
    if robot_id is not None:
        image_filters["robot_id"] = robot_id
    
    # Build point filters
    point_filters = {}
    if x_min is not None:
        point_filters["x_min"] = x_min
    if x_max is not None:
        point_filters["x_max"] = x_max
    if y_min is not None:
        point_filters["y_min"] = y_min
    if y_max is not None:
        point_filters["y_max"] = y_max
    if radius_min is not None:
        point_filters["radius_min"] = radius_min
    if radius_max is not None:
        point_filters["radius_max"] = radius_max
    
    # Get reduced embeddings for visualization
    reduced_embeddings = reduced_embedding_queries.get_reduced_embeddings_for_visualization(
        db=db,
        user_id=user_id,
        method=method,
        model_id=model_id,
        image_filters=image_filters if image_filters else None,
        point_filters=point_filters if point_filters else None,
        skip=0,
        limit=sample_size,
    )
    
    # Get bounds for scaling
    bounds = reduced_embedding_queries.get_reduced_embedding_bounds(
        db=db,
        user_id=user_id,
        method=method,
        model_id=model_id,
    )
    
    # Convert to response format
    points = [EmbeddingPoint3D(**data) for data in reduced_embeddings]
    
    return VisualizationResponse(
        method=method,
        model_id=model_id,
        total_points=len(points),
        points=points,
        bounds=bounds,
    )


@router.get("/filters/robots", response_model=List[str])
def get_available_robots(
    db: Session = Depends(get_db),
) -> List[str]:
    """Get list of available robot IDs for filtering."""
    # For now, using user_id = 3 (will be replaced with proper auth)
    user_id = 3
    
    return image_queries.get_unique_robot_ids(db=db, user_id=user_id)


@router.get("/filters/models", response_model=List[str])
def get_available_models(
    db: Session = Depends(get_db),
) -> List[str]:
    """Get list of available model IDs for filtering."""
    # For now, using user_id = 3 (will be replaced with proper auth)
    user_id = 3
    
    from ...database.queries import embeddings as embedding_queries
    return embedding_queries.get_unique_model_ids(db=db, user_id=user_id)


@router.get("/filters/methods", response_model=List[str])
def get_available_methods(
    db: Session = Depends(get_db),
) -> List[str]:
    """Get list of available reduction methods for filtering."""
    # For now, using user_id = 3 (will be replaced with proper auth)
    user_id = 3
    
    return reduced_embedding_queries.get_available_methods(db=db, user_id=user_id)
