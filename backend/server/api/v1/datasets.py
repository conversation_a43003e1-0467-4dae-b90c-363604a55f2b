"""Dataset API endpoints."""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional
from server.database import get_db, Dataset, DataPoint, Embedding, DatasetService, DataPointService, EmbeddingService
from server.api.v1.schemas import DatasetResponse, DatasetListResponse, EmbeddingPoint3D, EmbeddingResponse

router = APIRouter(prefix="/datasets", tags=["datasets"])


@router.get("/", response_model=DatasetListResponse)
async def list_datasets(
    skip: int = Query(0, ge=0, description="Number of datasets to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of datasets to return"),
    db: Session = Depends(get_db)
):
    """List all datasets with pagination."""
    # For now, get all datasets (we'll add user filtering later)
    datasets = db.query(Dataset).offset(skip).limit(limit).all()
    total = db.query(Dataset).count()

    return DatasetListResponse(
        datasets=[DatasetResponse.model_validate(dataset) for dataset in datasets],
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{dataset_id}", response_model=DatasetResponse)
async def get_dataset(dataset_id: int, db: Session = Depends(get_db)):
    """Get a specific dataset by ID."""
    dataset = DatasetService.get_dataset_by_id(db, dataset_id)
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")
    
    return DatasetResponse.model_validate(dataset)


@router.get("/{dataset_id}/embeddings", response_model=EmbeddingResponse)
async def get_dataset_embeddings(
    dataset_id: int,
    embedding_type: Optional[str] = Query(None, description="Filter by embedding type (e.g., 'umap_3d', 'original')"),
    limit: Optional[int] = Query(None, ge=1, le=100000, description="Maximum number of embeddings to return"),
    db: Session = Depends(get_db)
):
    """Get all embeddings for a dataset, optimized for visualization."""
    # Verify dataset exists
    dataset = DatasetService.get_dataset_by_id(db, dataset_id)
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")
    
    # Build query for embeddings with datapoint info
    query = db.query(
        Embedding.id,
        Embedding.embedding_type,
        Embedding.x,
        Embedding.y,
        Embedding.z,
        Embedding.dimensions,
        DataPoint.label,
        DataPoint.data_metadata,
        DataPoint.original_index
    ).join(
        DataPoint,
        Embedding.datapoint_id == DataPoint.id
    ).filter(
        DataPoint.dataset_id == dataset_id
    )

    # Filter by embedding type if specified
    if embedding_type:
        query = query.filter(Embedding.embedding_type == embedding_type)
    
    # Apply limit if specified
    if limit:
        query = query.limit(limit)
    
    embeddings = query.all()
    
    return EmbeddingResponse(
        dataset_id=dataset_id,
        embedding_type=embedding_type,
        total_points=len(embeddings),
        embeddings=[
            EmbeddingPoint3D(
                id=emb.id,
                x=emb.x if emb.x is not None else 0.0,
                y=emb.y if emb.y is not None else 0.0,
                z=emb.z if emb.z is not None else 0.0,
                label=emb.label,
                metadata=emb.data_metadata or {},
                embedding_type=emb.embedding_type,
                dimensions=emb.dimensions,
                original_index=emb.original_index
            )
            for emb in embeddings
        ]
    )


@router.get("/{dataset_id}/embeddings/3d", response_model=List[EmbeddingPoint3D])
async def get_dataset_embeddings_3d(
    dataset_id: int,
    embedding_type: str = Query("umap_3d", description="Type of 3D embedding to retrieve"),
    sample_size: Optional[int] = Query(100000, ge=1, le=1000000, description="Maximum number of points to return"),
    sampling_method: str = Query("random", description="Sampling method: random, systematic, or first_n"),
    seed: Optional[int] = Query(None, description="Random seed for reproducible sampling"),
    db: Session = Depends(get_db)
):
    """Get 3D embeddings for visualization with smart sampling for performance."""
    # Verify dataset exists
    dataset = DatasetService.get_dataset_by_id(db, dataset_id)
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")

    # Get total count for metadata
    total_count = EmbeddingService.get_embedding_count(db, dataset_id, embedding_type)
    if total_count == 0:
        raise HTTPException(
            status_code=404,
            detail=f"No 3D embeddings of type '{embedding_type}' found for dataset {dataset_id}"
        )

    # Determine actual sample size (don't sample if dataset is smaller than sample_size)
    actual_sample_size = min(sample_size, total_count)

    # Use sampling service for efficient data retrieval
    embeddings = EmbeddingService.get_embeddings_sample(
        db=db,
        dataset_id=dataset_id,
        embedding_type=embedding_type,
        sample_size=actual_sample_size,
        sampling_method=sampling_method,
        seed=seed
    )

    if not embeddings:
        raise HTTPException(
            status_code=404,
            detail=f"No 3D embeddings of type '{embedding_type}' found for dataset {dataset_id}"
        )

    # Convert to response format
    result = []
    for emb in embeddings:
        # Get the associated datapoint
        datapoint = DataPointService.get_datapoint_by_id(db, emb.datapoint_id)
        if datapoint and emb.x is not None and emb.y is not None and emb.z is not None:
            result.append(EmbeddingPoint3D(
                id=emb.id,
                x=emb.x,
                y=emb.y,
                z=emb.z,
                label=datapoint.label,
                metadata=datapoint.data_metadata or {},
                embedding_type=embedding_type,
                dimensions=3,
                original_index=datapoint.original_index
            ))

    return result


@router.get("/{dataset_id}/info")
async def get_dataset_info(dataset_id: int, db: Session = Depends(get_db)):
    """Get dataset metadata and statistics."""
    dataset = DatasetService.get_dataset_by_id(db, dataset_id)
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")
    
    # Get embedding statistics
    embedding_stats = db.query(
        Embedding.embedding_type,
        func.count(Embedding.id).label('count'),
        func.min(Embedding.x).label('min_x'),
        func.max(Embedding.x).label('max_x'),
        func.min(Embedding.y).label('min_y'),
        func.max(Embedding.y).label('max_y'),
        func.min(Embedding.z).label('min_z'),
        func.max(Embedding.z).label('max_z')
    ).join(
        DataPoint,
        Embedding.datapoint_id == DataPoint.id
    ).filter(
        DataPoint.dataset_id == dataset_id
    ).group_by(
        Embedding.embedding_type
    ).all()

    return {
        "dataset": DatasetResponse.model_validate(dataset),
        "embedding_types": [
            {
                "type": stat.embedding_type,
                "count": stat.count,
                "bounds": {
                    "x": [stat.min_x, stat.max_x] if stat.min_x is not None else None,
                    "y": [stat.min_y, stat.max_y] if stat.min_y is not None else None,
                    "z": [stat.min_z, stat.max_z] if stat.min_z is not None else None
                }
            }
            for stat in embedding_stats
        ]
    }
