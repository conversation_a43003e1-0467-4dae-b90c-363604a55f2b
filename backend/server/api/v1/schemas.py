"""Pydantic schemas for API responses."""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any


class ImageResponse(BaseModel):
    """Image response schema."""

    id: int
    user_id: int
    captured_at: int
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    height: int
    width: int
    robot_id: str
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    created_at: int
    updated_at: int
    total_points: Optional[int] = None

    class Config:
        from_attributes = True


class ImageListResponse(BaseModel):
    """Response for listing images."""

    images: List[ImageResponse]
    total: int
    skip: int
    limit: int


class ImageFilters(BaseModel):
    """Filters for image queries."""

    captured_after: Optional[int] = None
    captured_before: Optional[int] = None
    lat_min: Optional[float] = None
    lat_max: Optional[float] = None
    lng_min: Optional[float] = None
    lng_max: Optional[float] = None
    robot_id: Optional[List[str]] = None
    height_min: Optional[int] = None
    height_max: Optional[int] = None
    width_min: Optional[int] = None
    width_max: Optional[int] = None


class PointFilters(BaseModel):
    """Filters for point queries."""

    x_min: Optional[float] = None
    x_max: Optional[float] = None
    y_min: Optional[float] = None
    y_max: Optional[float] = None
    radius_min: Optional[float] = None
    radius_max: Optional[float] = None


class PointResponse(BaseModel):
    """Point response schema."""

    id: int
    image_id: int
    x: float
    y: float
    radius: float
    created_at: int
    updated_at: int
    # Optional image metadata
    image_captured_at: Optional[int] = None
    image_latitude: Optional[float] = None
    image_longitude: Optional[float] = None
    image_height: Optional[int] = None
    image_width: Optional[int] = None
    image_robot_id: Optional[str] = None

    class Config:
        from_attributes = True


class EmbeddingPoint3D(BaseModel):
    """3D embedding point for visualization."""

    id: int  # reduced_embedding id
    x: float
    y: float
    z: Optional[float] = None
    method: str
    model_id: str
    # Point data
    point_id: int
    point_x: float
    point_y: float
    point_radius: float
    # Image metadata for filtering/coloring
    image_id: int
    image_captured_at: int
    image_latitude: Optional[float] = None
    image_longitude: Optional[float] = None
    image_robot_id: str


class VisualizationResponse(BaseModel):
    """Response for 3D visualization data."""

    method: str
    model_id: Optional[str] = None
    total_points: int
    points: List[EmbeddingPoint3D]
    bounds: Dict[str, List[float]]  # x, y, z bounds for scaling


class VisualizationFilters(BaseModel):
    """Combined filters for visualization queries."""

    method: str = Field(..., description="Reduction method (e.g., 'umap_3d')")
    model_id: Optional[str] = None
    sample_size: Optional[int] = Field(50000, description="Maximum points to return")
    # Image filters
    image_filters: Optional[ImageFilters] = None
    # Point filters
    point_filters: Optional[PointFilters] = None


class UserResponse(BaseModel):
    """User response schema (excluding sensitive data)."""

    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    is_active: bool
    created_at: int
    updated_at: int

    class Config:
        from_attributes = True


class ErrorResponse(BaseModel):
    """Standard error response."""

    error: str
    message: str
    details: Optional[Dict[str, Any]] = None


class HealthResponse(BaseModel):
    """Health check response."""

    status: str
    service: str
    environment: str
    timestamp: int
    database_connected: bool = True


class APIStatusResponse(BaseModel):
    """API status response."""

    api_version: str
    status: str
    features: List[str]
    uptime_seconds: Optional[float] = None
