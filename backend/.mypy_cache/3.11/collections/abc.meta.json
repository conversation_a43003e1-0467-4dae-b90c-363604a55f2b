{"data_mtime": 1750879498, "dep_lines": [1, 1, 1, 1], "dep_prios": [5, 5, 30, 30], "dependencies": ["_collections_abc", "builtins", "abc", "typing"], "hash": "90189900dd153dff2aa642276e3a8a65145ed0f5eb67b8f1366086b38a3950e7", "id": "collections.abc", "ignore_all": true, "interface_hash": "4592a0b580d7a8d62ed60bf8ce4f89c09a6eb77f0e57c48c6232a48c8bad97f2", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/collections/abc.pyi", "plugin_data": null, "size": 79, "suppressed": [], "version_id": "1.7.1"}