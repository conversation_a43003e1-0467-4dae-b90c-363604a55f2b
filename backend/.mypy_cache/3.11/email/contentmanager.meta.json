{"data_mtime": 1750879498, "dep_lines": [1, 2, 3, 1, 1], "dep_prios": [5, 5, 5, 5, 30], "dependencies": ["collections.abc", "email.message", "typing", "builtins", "abc"], "hash": "53099e51c46e4530831d75440f30c0481378944b551b503d0689cd68c9afd1bf", "id": "email.contentmanager", "ignore_all": true, "interface_hash": "17d4ff326d406926063ddf2420da960608ff3f0238bdaa81137d04b46198e641", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/email/contentmanager.pyi", "plugin_data": null, "size": 480, "suppressed": [], "version_id": "1.7.1"}