{"data_mtime": 1750879498, "dep_lines": [2, 3, 4, 5, 6, 1, 7, 8, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5], "dependencies": ["collections.abc", "email.contentmanager", "email.errors", "email.header", "email.message", "abc", "typing", "typing_extensions", "builtins"], "hash": "abc933cf168f4f409ad3d06d89ac7bc2a557f6e4afc47d334079b24432de054c", "id": "email.policy", "ignore_all": true, "interface_hash": "570c0bd49b0dd040ada9ba396b544cd49f20275db6466b5018a733db070794a1", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/email/policy.pyi", "plugin_data": null, "size": 3088, "suppressed": [], "version_id": "1.7.1"}