{"data_mtime": 1750879498, "dep_lines": [1, 1, 1, 1, 1], "dep_prios": [10, 5, 30, 30, 30], "dependencies": ["sys", "builtins", "_typeshed", "abc", "typing"], "hash": "ca7498bfe12fcf0148a719c9bfa9f40ff037ddb6e1308f6101d338a8b9a859b9", "id": "email.errors", "ignore_all": true, "interface_hash": "9a4073a225c88fa266a71fc252695528ea0633c8268545c21acd94ee835e7dd1", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/email/errors.pyi", "plugin_data": null, "size": 1533, "suppressed": [], "version_id": "1.7.1"}