{"data_mtime": 1750879498, "dep_lines": [4, 5, 6, 2, 3, 7, 8, 9, 10, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "importlib.abc", "importlib.machinery", "_typeshed", "builtins", "io", "types", "typing", "typing_extensions", "abc", "importlib"], "hash": "3875cb12c43b878cd1051c753d609adecb4e6685e5ed6a18f0a2867c54a6870d", "id": "sys", "ignore_all": true, "interface_hash": "110dee093eb4708ce40cc53b7f06764ff7f32f8c00861b84a6c4f5459e8babbe", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/sys.pyi", "plugin_data": null, "size": 12601, "suppressed": [], "version_id": "1.7.1"}