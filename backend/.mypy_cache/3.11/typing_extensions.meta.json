{"data_mtime": 1750879498, "dep_lines": [1, 2, 3, 4, 5, 6, 74, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5], "dependencies": ["abc", "collections", "sys", "typing", "_collections_abc", "_typeshed", "types", "builtins"], "hash": "177270ea2ccd3a8436a2044659897e3109694bff93d73aeaf7b9ada17324982a", "id": "typing_extensions", "ignore_all": true, "interface_hash": "9580b6b765494238cf387a4cc2143527e76090a9c1cfe305c36b73d4ec9e979d", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/typing_extensions.pyi", "plugin_data": null, "size": 14936, "suppressed": [], "version_id": "1.7.1"}