{"data_mtime": 1750879498, "dep_lines": [3, 16, 1, 2, 19, 20, 631, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "importlib.machinery", "sys", "_typeshed", "typing", "typing_extensions", "builtins", "abc", "importlib"], "hash": "e2f776d03f16622ced3e8bb1d40171fc872c52204f3548070c1d728c5a434f88", "id": "types", "ignore_all": true, "interface_hash": "b5c5e6a578752984d5dfc734048ebf3477ae9516b6079c7d765a4e4e88add3f5", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/types.pyi", "plugin_data": null, "size": 21853, "suppressed": [], "version_id": "1.7.1"}