{"data_mtime": 1750879498, "dep_lines": [24, 31, 1, 2, 22, 23, 25, 26, 27, 28, 29, 34], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5], "dependencies": ["collections.abc", "os.path", "sys", "_typeshed", "abc", "builtins", "contextlib", "io", "subprocess", "typing", "typing_extensions", "types"], "hash": "19729ab1267bcdd48be8c053ecc1b9209e8608fcc65d51b06228f0f3edf5ebb2", "id": "os", "ignore_all": true, "interface_hash": "3f0ff3a03d20d3c44ca659f6a2432c292f5644c474a6c354af7dcba263ec7666", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/os/__init__.pyi", "plugin_data": null, "size": 39832, "suppressed": [], "version_id": "1.7.1"}