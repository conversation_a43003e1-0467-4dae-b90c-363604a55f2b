{"data_mtime": 1750879498, "dep_lines": [1, 7, 1, 1, 1], "dep_prios": [10, 5, 5, 30, 30], "dependencies": ["sys", "posixpath", "builtins", "abc", "typing"], "hash": "1bbead25bbe51b5fe4cc577c8270aa4b8321b7780fce50b58a1201ab3babc433", "id": "os.path", "ignore_all": true, "interface_hash": "3ef93be2d00d9d016eba0b290c62d7d197ba0aae385fec43b56b2164fdbc6926", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/os/path.pyi", "plugin_data": null, "size": 186, "suppressed": [], "version_id": "1.7.1"}