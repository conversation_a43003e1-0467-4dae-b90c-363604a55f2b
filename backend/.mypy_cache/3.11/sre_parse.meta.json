{"data_mtime": 1750879498, "dep_lines": [2, 1, 3, 4, 6, 7, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "sys", "re", "sre_constants", "typing", "typing_extensions", "builtins", "_typeshed", "abc"], "hash": "66feea8e50ec3199482f0f8c909ac9459518d21c54d6c5092009664a13926a4c", "id": "sre_parse", "ignore_all": true, "interface_hash": "ffebb1341769c2d8f9da0cca559f7bf4c82e15dad7d58ebc8641edef0983d163", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/sre_parse.pyi", "plugin_data": null, "size": 4485, "suppressed": [], "version_id": "1.7.1"}