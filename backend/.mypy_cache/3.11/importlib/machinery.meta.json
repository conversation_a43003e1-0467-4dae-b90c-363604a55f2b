{"data_mtime": 1750879498, "dep_lines": [1, 5, 9, 1, 2, 3, 4, 6, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 10, 10, 5, 5, 5, 30, 30], "dependencies": ["importlib.abc", "collections.abc", "importlib.metadata", "importlib", "sys", "types", "_typeshed", "typing", "builtins", "abc", "typing_extensions"], "hash": "b0573cd15671f878c6272cb1d946fc638f6ae213250e4285ffaa4c95aa64d67f", "id": "importlib.machinery", "ignore_all": true, "interface_hash": "f7dcd5d55eb33230d7bc426da5b5421a40a07b6075598057f63f9edcaf32df68", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/importlib/machinery.pyi", "plugin_data": null, "size": 5917, "suppressed": [], "version_id": "1.7.1"}