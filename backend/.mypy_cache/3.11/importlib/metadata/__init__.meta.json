{"data_mtime": 1750879498, "dep_lines": [31, 5, 6, 7, 1, 2, 3, 4, 8, 10, 11, 12, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 10, 5, 5, 5, 5, 5, 5], "dependencies": ["importlib.metadata._meta", "collections.abc", "email.message", "importlib.abc", "abc", "pathlib", "sys", "_typeshed", "os", "re", "typing", "typing_extensions", "builtins"], "hash": "7d6614b879d49ae65733d961cad98e620131ec99a9c9efc53fe564f3e1993fdd", "id": "importlib.metadata", "ignore_all": true, "interface_hash": "73cd08d29a594cf3163d0a524bf9fa636a7f5cac44c3fd5cdb758adf7fac47e6", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/importlib/metadata/__init__.pyi", "plugin_data": null, "size": 7147, "suppressed": [], "version_id": "1.7.1"}