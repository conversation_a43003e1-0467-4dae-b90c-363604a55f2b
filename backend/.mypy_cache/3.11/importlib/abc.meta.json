{"data_mtime": 1750879498, "dep_lines": [14, 15, 1, 2, 3, 4, 13, 16, 17, 18, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["collections.abc", "importlib.machinery", "_ast", "sys", "types", "_typeshed", "abc", "io", "typing", "typing_extensions", "builtins", "os"], "hash": "f8484e787449341feba703aeba5bbebdc24f9903a425757e020f5ba89544505b", "id": "importlib.abc", "ignore_all": true, "interface_hash": "77936b0b1979c8a5a3dd2d8b5b0f0e6c40ffe9f4595fff4833ad8e08b0b01f42", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/importlib/abc.pyi", "plugin_data": null, "size": 8248, "suppressed": [], "version_id": "1.7.1"}