{"data_mtime": 1750879498, "dep_lines": [5, 1, 2, 3, 4, 6, 7, 8, 11, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["collections.abc", "enum", "sre_compile", "sys", "_typeshed", "sre_constants", "typing", "typing_extensions", "types", "builtins", "abc"], "hash": "e4563eb3a3eab32b59d93bdb039e75f5d03e4ffbc3beec237ceb39ecdd0f69e1", "id": "re", "ignore_all": true, "interface_hash": "d1e11475f5278082b80e629a0ba248def97aa8190ec2772e9d020d2f98080906", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/re.pyi", "plugin_data": null, "size": 11221, "suppressed": [], "version_id": "1.7.1"}