{"data_mtime": 1750879498, "dep_lines": [6, 1, 2, 3, 4, 5, 7, 8, 9, 10], "dep_prios": [5, 10, 10, 10, 10, 5, 5, 5, 5, 5], "dependencies": ["collections.abc", "abc", "builtins", "codecs", "sys", "_typeshed", "os", "types", "typing", "typing_extensions"], "hash": "0b41ad24faa49ac20e0c8c0d0bf223ff7de8ce8ea44fe01e3a4a2cb1f363054d", "id": "io", "ignore_all": true, "interface_hash": "5c8a7751493cc2f3755ccf0c070ef6c974605d6748895027db303faed3c7eb38", "mtime": 1750795165, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/usr/local/lib/python3.11/site-packages/mypy/typeshed/stdlib/io.pyi", "plugin_data": null, "size": 7875, "suppressed": [], "version_id": "1.7.1"}