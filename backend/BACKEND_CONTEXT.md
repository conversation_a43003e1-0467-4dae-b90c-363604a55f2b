# Backend Context

This file provides information about the design decisions and architecture of the backend codebase.

## Overview

- The backend is built using FastAPI and SQLAlchemy
- The database is PostgreSQL

## Directory Structure

Functionality in the server directory is organized into two broad categories: database interactions and API endpoints. 
API endpoints are stored in the api directory and database interactions are stored in the database directory. The
database directory contains a tables.py file that defines the entire database schema using SQLAlchemy models. Next to
the queries.py file is a queries directory that contains a single file per table. Each file contains queries for that
table. 