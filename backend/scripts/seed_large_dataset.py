#!/usr/bin/env python3
"""Large dataset seeding script for performance testing."""

import argparse
import sys
import os
import numpy as np
import time
from typing import List, <PERSON><PERSON>

# Add the parent directory to the path so we can import server modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database import SessionLocal, current_timestamp_ms
from server.database.queries import users, datasets, datapoints, embeddings


def create_performance_user(db) -> int:
    """Create or get the performance testing user."""
    username = "atlas_stormweaver"
    email = "<EMAIL>"
    full_name = "Atlas Stormweaver"
    
    # Check if user already exists
    existing_user = users.get_by_username(db, username)
    if existing_user:
        print(f"Using existing performance user: {full_name}")
        return existing_user.id

    # Create new user
    user = users.create(
        db=db,
        username=username,
        email=email,
        hashed_password="perf_test_hash_456",
        full_name=full_name
    )
    print(f"Created performance user: {full_name}")
    return user.id


def create_large_dataset(db, user_id: int, n_points: int) -> int:
    """Create or get the large performance dataset."""
    dataset_name = f"Cosmic Data Constellation ({n_points:,} points)"
    
    # Check if dataset already exists
    existing_datasets = datasets.get_by_owner(db, user_id)
    for dataset in existing_datasets:
        if dataset.name == dataset_name:
            print(f"Using existing dataset: {dataset_name}")
            return dataset.id

    # Create new dataset
    dataset = datasets.create(
        db=db,
        name=dataset_name,
        owner_id=user_id,
        description=f"Large-scale performance testing dataset with {n_points:,} points for 3D visualization stress testing"
    )
    
    # Update dataset metadata
    dataset.total_points = n_points
    dataset.embedding_dimension = 512
    dataset.status = "ready"
    dataset.processed_at = current_timestamp_ms()
    db.commit()
    
    print(f"Created dataset: {dataset_name}")
    return dataset.id


def generate_large_clustered_embeddings(n_points: int = 1000000, n_dims: int = 512, n_clusters: int = 20) -> Tuple[np.ndarray, np.ndarray]:
    """Generate realistic large-scale clustered embeddings and 3D coordinates."""
    print(f"🎲 Generating {n_points:,} embeddings with {n_clusters} clusters...")
    np.random.seed(42)  # For reproducible results
    
    # Generate cluster centers in high-dimensional space
    cluster_centers = np.random.randn(n_clusters, n_dims) * 3
    # Generate cluster centers in 3D space (more spread out for better visualization)
    cluster_centers_3d = np.random.randn(n_clusters, 3) * 15
    
    # Assign points to clusters with some randomness
    cluster_assignments = np.random.choice(n_clusters, n_points)
    
    # Pre-allocate arrays for better memory efficiency
    embeddings = np.zeros((n_points, n_dims), dtype=np.float32)
    coords_3d = np.zeros((n_points, 3), dtype=np.float32)
    
    print("📊 Generating point coordinates...")
    batch_size = 50000  # Process in batches to manage memory
    
    for batch_start in range(0, n_points, batch_size):
        batch_end = min(batch_start + batch_size, n_points)
        batch_size_actual = batch_end - batch_start
        
        if batch_start % 200000 == 0:
            print(f"  Processing embedding batch {batch_start:,} - {batch_end:,}")
        
        # Get cluster assignments for this batch
        batch_clusters = cluster_assignments[batch_start:batch_end]
        
        # Generate embeddings for this batch
        for i, cluster_id in enumerate(batch_clusters):
            global_idx = batch_start + i
            # Add noise around cluster center (smaller noise for more defined clusters)
            embeddings[global_idx] = cluster_centers[cluster_id] + np.random.randn(n_dims).astype(np.float32) * 0.3
            coords_3d[global_idx] = cluster_centers_3d[cluster_id] + np.random.randn(3).astype(np.float32) * 2.0
    
    print("🔄 Normalizing embeddings...")
    # Normalize embeddings in batches to manage memory
    for batch_start in range(0, n_points, batch_size):
        batch_end = min(batch_start + batch_size, n_points)
        batch_embeddings = embeddings[batch_start:batch_end]
        norms = np.linalg.norm(batch_embeddings, axis=1, keepdims=True)
        norms[norms == 0] = 1  # Avoid division by zero
        embeddings[batch_start:batch_end] = batch_embeddings / norms
    
    return embeddings, coords_3d


def generate_large_labels(n_points: int) -> List[str]:
    """Generate labels for large dataset."""
    creatures = [
        "Cosmic_Phoenix", "Stellar_Dragon", "Nebula_Unicorn", "Quantum_Wolf",
        "Galactic_Eagle", "Astral_Tiger", "Solar_Bear", "Lunar_Fox",
        "Void_Serpent", "Plasma_Butterfly", "Meteor_Lion", "Comet_Deer",
        "Aurora_Owl", "Pulsar_Rabbit", "Quasar_Swan", "Binary_Hawk",
        "Supernova_Cat", "Blackhole_Whale", "Starlight_Dolphin", "Cosmic_Elephant"
    ]
    
    # Pre-generate labels in batches for memory efficiency
    labels = []
    batch_size = 10000
    
    print("🏷️  Generating labels...")
    for batch_start in range(0, n_points, batch_size):
        batch_end = min(batch_start + batch_size, n_points)
        
        if batch_start % 100000 == 0:
            print(f"  Generating labels {batch_start:,} - {batch_end:,}")
        
        batch_labels = []
        for i in range(batch_start, batch_end):
            creature = creatures[i % len(creatures)]
            number = str(i + 1).zfill(7)  # 7 digits for up to 10M points
            batch_labels.append(f"{creature}_{number}")
        
        labels.extend(batch_labels)
    
    return labels


def seed_large_data(reset: bool = False, n_points: int = 1000000):
    """Seed the database with large-scale performance testing data."""
    db = SessionLocal()
    
    try:
        print(f"🚀 Seeding large-scale performance data ({n_points:,} points)...")
        print(f"⚠️  This will take significant time and resources!")
        
        if reset:
            print("🗑️  Clearing existing large dataset data...")
            from sqlalchemy import text
            # Only clear data for the performance user
            db.execute(text("DELETE FROM embeddings WHERE datapoint_id IN (SELECT id FROM datapoints WHERE dataset_id IN (SELECT id FROM datasets WHERE owner_id = (SELECT id FROM users WHERE username = 'atlas_stormweaver')))"))
            db.execute(text("DELETE FROM datapoints WHERE dataset_id IN (SELECT id FROM datasets WHERE owner_id = (SELECT id FROM users WHERE username = 'atlas_stormweaver'))"))
            db.execute(text("DELETE FROM datasets WHERE owner_id = (SELECT id FROM users WHERE username = 'atlas_stormweaver')"))
            db.execute(text("DELETE FROM users WHERE username = 'atlas_stormweaver'"))
            db.commit()
        
        # Create user and dataset
        user_id = create_performance_user(db)
        dataset_id = create_large_dataset(db, user_id, n_points)
        
        # Check if data already exists
        existing_points = datapoints.get_by_dataset(db, dataset_id)
        if existing_points and not reset:
            print(f"Dataset already has {len(existing_points)} data points. Use --reset to recreate.")
            return
        
        # Generate embeddings and coordinates
        start_time = time.time()
        embeddings, coords_3d = generate_large_clustered_embeddings(n_points)
        labels = generate_large_labels(n_points)
        generation_time = time.time() - start_time
        print(f"⏱️  Data generation completed in {generation_time:.1f} seconds")
        
        # Insert data in larger batches for better performance
        batch_size = 1000  # Larger batches for better performance
        print(f"💾 Inserting {n_points:,} data points in batches of {batch_size}...")
        
        insert_start_time = time.time()
        
        for i in range(0, n_points, batch_size):
            batch_end = min(i + batch_size, n_points)
            batch_num = i // batch_size + 1
            total_batches = (n_points + batch_size - 1) // batch_size
            
            if batch_num % 100 == 0 or batch_num == 1:
                elapsed = time.time() - insert_start_time
                rate = i / elapsed if elapsed > 0 else 0
                eta = (n_points - i) / rate if rate > 0 else 0
                print(f"  Batch {batch_num:,}/{total_batches:,} | Points: {i:,}/{n_points:,} | Rate: {rate:.0f}/sec | ETA: {eta:.0f}s")
            
            batch_datapoints = []
            batch_embeddings_orig = []
            batch_embeddings_3d = []
            
            # Prepare batch data
            for j in range(i, batch_end):
                # Prepare datapoint data
                creature_parts = labels[j].split('_')
                creature_type = f"{creature_parts[0]}_{creature_parts[1]}"
                
                datapoint_data = {
                    'dataset_id': dataset_id,
                    'label': labels[j],
                    'data_metadata': {
                        "creature_type": creature_type,
                        "magic_level": int(np.random.randint(1, 11)),
                        "rarity": np.random.choice(["common", "uncommon", "rare", "legendary"]),
                        "sector": f"Sector_{(j // 10000) + 1}"  # Group into sectors for organization
                    },
                    'original_index': j
                }
                batch_datapoints.append(datapoint_data)
            
            # Insert datapoints in batch
            for dp_data in batch_datapoints:
                datapoint = datapoints.create(db=db, **dp_data)

                # Create original embedding
                embeddings.create(
                    db=db,
                    datapoint_id=datapoint.id,
                    embedding_type="original",
                    dimensions=512,
                    vector=embeddings[dp_data['original_index']].tolist(),
                    method_params={"model": "cosmic_encoder_v2.0", "batch_size": batch_size}
                )

                # Create 3D visualization embedding
                coord_idx = dp_data['original_index']
                embeddings.create(
                    db=db,
                    datapoint_id=datapoint.id,
                    embedding_type="umap_3d",
                    dimensions=3,
                    vector=coords_3d[coord_idx].tolist(),
                    method_params={"n_neighbors": 15, "min_dist": 0.1, "n_clusters": 20},
                    x=float(coords_3d[coord_idx][0]),
                    y=float(coords_3d[coord_idx][1]),
                    z=float(coords_3d[coord_idx][2])
                )
            
            # Commit every 10 batches to avoid huge transactions
            if batch_num % 10 == 0:
                db.commit()
        
        # Final commit
        db.commit()
        
        total_time = time.time() - start_time
        insert_time = time.time() - insert_start_time
        
        print("🎉 Large-scale data seeding completed successfully!")
        print(f"👤 User: Atlas Stormweaver")
        print(f"📁 Dataset: Cosmic Data Constellation ({n_points:,} points)")
        print(f"📊 Data points: {n_points:,}")
        print(f"🎯 Embeddings: {n_points * 2:,} (original + 3D)")
        print(f"⏱️  Total time: {total_time:.1f}s | Insert time: {insert_time:.1f}s")
        print(f"🚀 Average rate: {n_points / insert_time:.0f} points/second")
        
    except Exception as e:
        print(f"❌ Error seeding large data: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Seed large-scale performance data for Loom")
    parser.add_argument("--reset", action="store_true", help="Clear existing large dataset data first")
    parser.add_argument("--points", type=int, default=1000000, help="Number of data points to create (default: 1M)")
    
    args = parser.parse_args()
    
    if args.points > 5000000:
        print("⚠️  Warning: Creating more than 5M points may take hours and require significant resources!")
        response = input("Continue? (y/N): ")
        if response.lower() != 'y':
            print("Cancelled.")
            return
    
    try:
        seed_large_data(reset=args.reset, n_points=args.points)
    except Exception as e:
        print(f"Failed to seed large data: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
