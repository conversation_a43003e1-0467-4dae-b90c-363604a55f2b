#!/usr/bin/env python3
"""
Professional dataset seeding script for Loom.
Creates realistic, professional datasets for development and testing.
"""

import sys
import os
import numpy as np
from typing import List, Dict

# Add the parent directory to the path so we can import server modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database.connection import SessionLocal
from server.database.queries import users, datasets, datapoints, embeddings


def create_professional_user(db) -> int:
    """Create or get the professional demo user."""
    username = "demo_analyst"

    # Check if user already exists
    existing_user = users.get_by_username(db, username)
    if existing_user:
        print(f"Using existing user: {username}")
        return existing_user.id

    # Create new user
    user = users.create(
        db=db,
        username=username,
        email="<EMAIL>",
        hashed_password="demo_password_hash",
        full_name="Demo Data Analyst",
    )

    print(f"Created user: {username}")
    return user.id


def create_research_papers_dataset(db, user_id: int) -> int:
    """Create a dataset of research paper embeddings."""
    dataset_name = "Research Paper Embeddings"

    # Check if dataset already exists
    existing_datasets = datasets.get_by_owner(db, user_id)
    for dataset in existing_datasets:
        if dataset.name == dataset_name:
            print(f"Using existing dataset: {dataset_name}")
            return dataset.id

    # Create new dataset
    dataset = datasets.create(
        db=db,
        name=dataset_name,
        owner_id=user_id,
        description="Scientific research paper embeddings from arXiv abstracts across multiple domains",
    )

    print(f"Created dataset: {dataset_name}")
    return dataset.id


def create_customer_segments_dataset(db, user_id: int) -> int:
    """Create a customer segmentation dataset."""
    dataset_name = "Customer Behavior Segments"

    # Check if dataset already exists
    existing_datasets = datasets.get_by_owner(db, user_id)
    for dataset in existing_datasets:
        if dataset.name == dataset_name:
            print(f"Using existing dataset: {dataset_name}")
            return dataset.id

    # Create new dataset
    dataset = datasets.create(
        db=db,
        name=dataset_name,
        owner_id=user_id,
        description="Customer behavioral data embeddings for market segmentation analysis",
    )

    print(f"Created dataset: {dataset_name}")
    return dataset.id


def create_product_catalog_dataset(db, user_id: int) -> int:
    """Create a product catalog dataset."""
    dataset_name = "E-commerce Product Catalog"

    # Check if dataset already exists
    existing_datasets = datasets.get_by_owner(db, user_id)
    for dataset in existing_datasets:
        if dataset.name == dataset_name:
            print(f"Using existing dataset: {dataset_name}")
            return dataset.id

    # Create new dataset
    dataset = datasets.create(
        db=db,
        name=dataset_name,
        owner_id=user_id,
        description="Product embeddings from e-commerce catalog for recommendation systems",
    )

    print(f"Created dataset: {dataset_name}")
    return dataset.id


def generate_research_paper_data(n_points: int) -> tuple:
    """Generate realistic research paper data."""
    domains = [
        "Computer Science",
        "Physics",
        "Biology",
        "Mathematics",
        "Chemistry",
        "Medicine",
        "Engineering",
    ]
    subfields = {
        "Computer Science": [
            "Machine Learning",
            "Computer Vision",
            "Natural Language Processing",
            "Robotics",
            "Security",
        ],
        "Physics": [
            "Quantum Physics",
            "Astrophysics",
            "Condensed Matter",
            "Particle Physics",
            "Optics",
        ],
        "Biology": [
            "Molecular Biology",
            "Genetics",
            "Ecology",
            "Neuroscience",
            "Bioinformatics",
        ],
        "Mathematics": [
            "Algebra",
            "Analysis",
            "Topology",
            "Statistics",
            "Number Theory",
        ],
        "Chemistry": [
            "Organic Chemistry",
            "Physical Chemistry",
            "Biochemistry",
            "Materials Science",
        ],
        "Medicine": [
            "Cardiology",
            "Oncology",
            "Neurology",
            "Immunology",
            "Pharmacology",
        ],
        "Engineering": ["Electrical", "Mechanical", "Civil", "Chemical", "Biomedical"],
    }

    venues = [
        "Nature",
        "Science",
        "Cell",
        "PNAS",
        "IEEE",
        "ACM",
        "Springer",
        "Elsevier",
    ]

    labels = []
    metadata_list = []

    for i in range(n_points):
        domain = np.random.choice(domains)
        subfield = np.random.choice(subfields[domain])
        venue = np.random.choice(venues)
        year = np.random.randint(2018, 2024)

        paper_id = f"paper_{str(i+1).zfill(6)}"
        labels.append(paper_id)

        metadata_list.append(
            {
                "domain": domain,
                "subfield": subfield,
                "venue": venue,
                "publication_year": year,
                "citation_count": int(np.random.exponential(10)),
                "impact_factor": round(np.random.uniform(1.0, 15.0), 2),
            }
        )

    return labels, metadata_list


def generate_customer_data(n_points: int) -> tuple:
    """Generate realistic customer segmentation data."""
    segments = ["High Value", "Regular", "Price Sensitive", "New Customer", "At Risk"]
    regions = [
        "North America",
        "Europe",
        "Asia Pacific",
        "Latin America",
        "Middle East",
    ]
    channels = ["Online", "Mobile App", "Retail Store", "Phone", "Email"]

    labels = []
    metadata_list = []

    for i in range(n_points):
        customer_id = f"customer_{str(i+1).zfill(8)}"
        labels.append(customer_id)

        metadata_list.append(
            {
                "segment": np.random.choice(segments),
                "region": np.random.choice(regions),
                "primary_channel": np.random.choice(channels),
                "lifetime_value": round(np.random.uniform(50, 5000), 2),
                "purchase_frequency": int(np.random.poisson(8)),
                "satisfaction_score": round(np.random.uniform(1.0, 5.0), 1),
            }
        )

    return labels, metadata_list


def generate_product_data(n_points: int) -> tuple:
    """Generate realistic product catalog data."""
    categories = [
        "Electronics",
        "Clothing",
        "Home & Garden",
        "Sports",
        "Books",
        "Health",
        "Automotive",
    ]
    brands = ["BrandA", "BrandB", "BrandC", "BrandD", "BrandE", "Generic"]
    conditions = ["New", "Refurbished", "Used"]

    labels = []
    metadata_list = []

    for i in range(n_points):
        product_id = f"product_{str(i+1).zfill(7)}"
        labels.append(product_id)

        metadata_list.append(
            {
                "category": np.random.choice(categories),
                "brand": np.random.choice(brands),
                "condition": np.random.choice(conditions),
                "price": round(np.random.uniform(10, 1000), 2),
                "rating": round(np.random.uniform(1.0, 5.0), 1),
                "review_count": int(np.random.exponential(50)),
            }
        )

    return labels, metadata_list


def create_embeddings_for_dataset(
    db,
    dataset_id: int,
    labels: List[str],
    metadata_list: List[Dict],
    embedding_dim: int = 512,
):
    """Create embeddings for a dataset."""
    n_points = len(labels)

    # Generate realistic embeddings with some structure
    embeddings_orig = np.random.normal(0, 0.1, (n_points, embedding_dim))

    # Create 3D embeddings using dimensionality reduction simulation
    # Simulate UMAP-like embedding with some clustering
    n_clusters = min(10, n_points // 50)
    cluster_centers = np.random.uniform(-10, 10, (n_clusters, 3))

    embeddings_3d = []
    for i in range(n_points):
        cluster_idx = i % n_clusters
        center = cluster_centers[cluster_idx]
        noise = np.random.normal(0, 2, 3)
        point_3d = center + noise
        embeddings_3d.append(point_3d)

    embeddings_3d = np.array(embeddings_3d)

    # Batch insert for better performance
    batch_size = 1000
    for i in range(0, n_points, batch_size):
        batch_end = min(i + batch_size, n_points)
        print(f"  Processing batch {i//batch_size + 1}/{(n_points-1)//batch_size + 1}")

        for j in range(i, batch_end):
            # Create datapoint
            datapoint = datapoints.create(
                db=db,
                dataset_id=dataset_id,
                label=labels[j],
                data_metadata=metadata_list[j],
                original_index=j,
            )

            # Create original embedding
            embeddings.create(
                db=db,
                datapoint_id=datapoint.id,
                embedding_type="original",
                dimensions=embedding_dim,
                vector=embeddings_orig[j].tolist(),
                method_params={"model": "sentence-transformers/all-MiniLM-L6-v2"},
            )

            # Create 3D embedding
            embeddings.create(
                db=db,
                datapoint_id=datapoint.id,
                embedding_type="umap_3d",
                dimensions=3,
                vector=embeddings_3d[j].tolist(),
                x=float(embeddings_3d[j][0]),
                y=float(embeddings_3d[j][1]),
                z=float(embeddings_3d[j][2]),
                method_params={"algorithm": "umap", "n_neighbors": 15, "min_dist": 0.1},
            )

        db.commit()


def seed_professional_data(reset: bool = False):
    """Seed the database with professional datasets."""
    db = SessionLocal()

    try:
        print("🏢 Seeding professional datasets...")

        if reset:
            print("🗑️  Clearing existing data...")
            from sqlalchemy import text

            db.execute(text("DELETE FROM embeddings"))
            db.execute(text("DELETE FROM datapoints"))
            db.execute(text("DELETE FROM datasets"))
            db.execute(text("DELETE FROM users WHERE username = 'demo_analyst'"))
            db.commit()

        # Create user
        user_id = create_professional_user(db)

        # Dataset 1: Research Papers (1,500 points)
        print("\n📚 Creating Research Paper Embeddings dataset...")
        dataset_id = create_research_papers_dataset(db, user_id)
        existing_points = datapoints.get_by_dataset(db, dataset_id)
        if not existing_points or reset:
            labels, metadata_list = generate_research_paper_data(1500)
            create_embeddings_for_dataset(db, dataset_id, labels, metadata_list)

        # Dataset 2: Customer Segments (2,000 points)
        print("\n👥 Creating Customer Behavior Segments dataset...")
        dataset_id = create_customer_segments_dataset(db, user_id)
        existing_points = datapoints.get_by_dataset(db, dataset_id)
        if not existing_points or reset:
            labels, metadata_list = generate_customer_data(2000)
            create_embeddings_for_dataset(db, dataset_id, labels, metadata_list)

        # Dataset 3: Product Catalog (3,000 points)
        print("\n🛍️  Creating E-commerce Product Catalog dataset...")
        dataset_id = create_product_catalog_dataset(db, user_id)
        existing_points = datapoints.get_by_dataset(db, dataset_id)
        if not existing_points or reset:
            labels, metadata_list = generate_product_data(3000)
            create_embeddings_for_dataset(db, dataset_id, labels, metadata_list)

        print("\n✅ Professional datasets created successfully!")

    except Exception as e:
        print(f"❌ Error seeding data: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Seed professional datasets")
    parser.add_argument("--reset", action="store_true", help="Reset existing data")

    args = parser.parse_args()
    seed_professional_data(reset=args.reset)
