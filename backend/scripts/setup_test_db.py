#!/usr/bin/env python3
"""<PERSON><PERSON><PERSON> to set up the test database for integration tests."""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Add the parent directory to the path so we can import server modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def create_test_database():
    """Create the test database if it doesn't exist."""
    # Connection parameters for the main database
    # Use 'postgres' as host when running inside Docker, 'localhost' otherwise
    host = os.getenv("TEST_DB_HOST", "postgres" if os.getenv("ENVIRONMENT") else "localhost")
    port = os.getenv("TEST_DB_PORT", "5432")
    user = os.getenv("TEST_DB_USER", "loom_user")
    password = os.getenv("TEST_DB_PASSWORD", "loom_password")
    test_db_name = os.getenv("TEST_DB_NAME", "loom_test")
    
    # Connect to PostgreSQL server (not to a specific database)
    try:
        # Connect to the default 'postgres' database to create our test database
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database="postgres"  # Connect to default database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check if test database exists
        cursor.execute(
            "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
            (test_db_name,)
        )
        exists = cursor.fetchone()
        
        if not exists:
            print(f"Creating test database: {test_db_name}")
            cursor.execute(f'CREATE DATABASE "{test_db_name}"')
            print(f"Test database '{test_db_name}' created successfully!")
        else:
            print(f"Test database '{test_db_name}' already exists.")
        
        cursor.close()
        conn.close()
        
    except psycopg2.Error as e:
        print(f"Error creating test database: {e}")
        sys.exit(1)


def drop_test_database():
    """Drop the test database."""
    host = os.getenv("TEST_DB_HOST", "postgres" if os.getenv("ENVIRONMENT") else "localhost")
    port = os.getenv("TEST_DB_PORT", "5432")
    user = os.getenv("TEST_DB_USER", "loom_user")
    password = os.getenv("TEST_DB_PASSWORD", "loom_password")
    test_db_name = os.getenv("TEST_DB_NAME", "loom_test")
    
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database="postgres"
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Terminate existing connections to the test database
        cursor.execute("""
            SELECT pg_terminate_backend(pid)
            FROM pg_stat_activity
            WHERE datname = %s AND pid <> pg_backend_pid()
        """, (test_db_name,))
        
        # Drop the database
        cursor.execute(f'DROP DATABASE IF EXISTS "{test_db_name}"')
        print(f"Test database '{test_db_name}' dropped successfully!")
        
        cursor.close()
        conn.close()
        
    except psycopg2.Error as e:
        print(f"Error dropping test database: {e}")
        sys.exit(1)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Manage test database")
    parser.add_argument("action", choices=["create", "drop"], help="Action to perform")
    
    args = parser.parse_args()
    
    if args.action == "create":
        create_test_database()
    elif args.action == "drop":
        drop_test_database()
