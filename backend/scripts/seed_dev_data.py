#!/usr/bin/env python3
"""Development data seeding script for Loom."""

import argparse
import sys
import os
import numpy as np
from typing import List, <PERSON><PERSON>

# Add the parent directory to the path so we can import server modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database import SessionLocal, current_timestamp_ms
from server.database.queries import users, datasets, datapoints, embeddings


def create_dev_user(db) -> int:
    """Create or get the development user."""
    username = "penelope_featherworth"
    email = "<EMAIL>"
    full_name = "<PERSON> Featherworth"
    
    # Check if user already exists
    existing_user = users.get_by_username(db, username)
    if existing_user:
        print(f"Using existing dev user: {full_name}")
        return existing_user.id

    # Create new user
    user = users.create(
        db=db,
        username=username,
        email=email,
        hashed_password="dev_password_hash_123",
        full_name=full_name
    )
    print(f"Created dev user: {full_name}")
    return user.id


def create_sample_dataset(db, user_id: int) -> int:
    """Create or get the sample dataset."""
    dataset_name = "Enchanted Vector Menagerie"
    
    # Check if dataset already exists
    existing_datasets = datasets.get_by_owner(db, user_id)
    for dataset in existing_datasets:
        if dataset.name == dataset_name:
            print(f"Using existing dataset: {dataset_name}")
            return dataset.id

    # Create new dataset
    dataset = datasets.create(
        db=db,
        name=dataset_name,
        owner_id=user_id,
        description="A whimsical collection of magical creature embeddings for development"
    )
    
    # Update dataset metadata
    dataset.total_points = 1000
    dataset.embedding_dimension = 512
    dataset.status = "ready"
    dataset.processed_at = current_timestamp_ms()
    db.commit()
    
    print(f"Created dataset: {dataset_name}")
    return dataset.id


def generate_clustered_embeddings(n_points: int = 1000, n_dims: int = 512, n_clusters: int = 5) -> Tuple[np.ndarray, np.ndarray]:
    """Generate realistic clustered embeddings and 3D coordinates."""
    np.random.seed(42)  # For reproducible results
    
    # Generate cluster centers
    cluster_centers = np.random.randn(n_clusters, n_dims) * 2
    cluster_centers_3d = np.random.randn(n_clusters, 3) * 5
    
    # Assign points to clusters
    cluster_assignments = np.random.choice(n_clusters, n_points)
    
    # Generate embeddings around cluster centers
    embeddings = np.zeros((n_points, n_dims))
    coords_3d = np.zeros((n_points, 3))
    
    for i in range(n_points):
        cluster_id = cluster_assignments[i]
        # Add noise around cluster center
        embeddings[i] = cluster_centers[cluster_id] + np.random.randn(n_dims) * 0.5
        coords_3d[i] = cluster_centers_3d[cluster_id] + np.random.randn(3) * 1.5
    
    # Normalize embeddings
    embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
    
    return embeddings, coords_3d


def generate_whimsical_labels(n_points: int) -> List[str]:
    """Generate whimsical creature labels."""
    creatures = [
        "Sparkly_Unicorn", "Dancing_Dragon", "Mystical_Phoenix", "Giggling_Goblin",
        "Wise_Owl", "Bouncing_Butterfly", "Sleepy_Bear", "Curious_Cat",
        "Majestic_Eagle", "Playful_Dolphin", "Gentle_Deer", "Brave_Lion",
        "Clever_Fox", "Happy_Rabbit", "Graceful_Swan", "Mighty_Wolf",
        "Colorful_Parrot", "Peaceful_Dove", "Swift_Cheetah", "Strong_Elephant"
    ]
    
    labels = []
    for i in range(n_points):
        creature = creatures[i % len(creatures)]
        number = str(i + 1).zfill(3)
        labels.append(f"{creature}_{number}")
    
    return labels


def seed_data(reset: bool = False, n_points: int = 1000):
    """Seed the database with development data."""
    db = SessionLocal()
    
    try:
        print(f"🌱 Seeding development data ({n_points} points)...")
        
        if reset:
            print("🗑️  Clearing existing data...")
            # Note: In a real scenario, you might want more sophisticated cleanup
            from sqlalchemy import text
            db.execute(text("DELETE FROM embeddings"))
            db.execute(text("DELETE FROM datapoints"))
            db.execute(text("DELETE FROM datasets"))
            db.execute(text("DELETE FROM users WHERE username = 'penelope_featherworth'"))
            db.commit()
        
        # Create user and dataset
        user_id = create_dev_user(db)
        dataset_id = create_sample_dataset(db, user_id)
        
        # Check if data already exists
        existing_points = datapoints.get_by_dataset(db, dataset_id)
        if existing_points and not reset:
            print(f"Dataset already has {len(existing_points)} data points. Use --reset to recreate.")
            return
        
        # Generate embeddings and coordinates
        print("🎲 Generating embeddings...")
        embeddings, coords_3d = generate_clustered_embeddings(n_points)
        labels = generate_whimsical_labels(n_points)
        
        # Insert data in batches
        batch_size = 100
        print(f"📊 Inserting {n_points} data points...")
        
        for i in range(0, n_points, batch_size):
            batch_end = min(i + batch_size, n_points)
            print(f"  Processing batch {i//batch_size + 1}/{(n_points + batch_size - 1)//batch_size}...")
            
            for j in range(i, batch_end):
                # Create datapoint
                datapoint = datapoints.create(
                    db=db,
                    dataset_id=dataset_id,
                    label=labels[j],
                    data_metadata={
                        "creature_type": labels[j].split('_')[0] + "_" + labels[j].split('_')[1],
                        "magic_level": np.random.randint(1, 11),
                        "rarity": np.random.choice(["common", "uncommon", "rare", "legendary"])
                    },
                    original_index=j
                )

                # Create original embedding
                embeddings.create(
                    db=db,
                    datapoint_id=datapoint.id,
                    embedding_type="original",
                    dimensions=512,
                    vector=embeddings[j].tolist(),
                    method_params={"model": "magical_encoder_v1.0"}
                )

                # Create 3D visualization embedding
                embeddings.create(
                    db=db,
                    datapoint_id=datapoint.id,
                    embedding_type="umap_3d",
                    dimensions=3,
                    vector=coords_3d[j].tolist(),
                    method_params={"n_neighbors": 15, "min_dist": 0.1},
                    x=coords_3d[j][0],
                    y=coords_3d[j][1],
                    z=coords_3d[j][2]
                )
        
        print("✨ Development data seeding completed successfully!")
        print(f"👤 User: Penelope Featherworth")
        print(f"📁 Dataset: Enchanted Vector Menagerie")
        print(f"📊 Data points: {n_points}")
        print(f"🎯 Embeddings: {n_points * 2} (original + 3D)")
        
    except Exception as e:
        print(f"❌ Error seeding data: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Seed development data for Loom")
    parser.add_argument("--reset", action="store_true", help="Clear existing data first")
    parser.add_argument("--points", type=int, default=1000, help="Number of data points to create")
    
    args = parser.parse_args()
    
    try:
        seed_data(reset=args.reset, n_points=args.points)
    except Exception as e:
        print(f"Failed to seed data: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
