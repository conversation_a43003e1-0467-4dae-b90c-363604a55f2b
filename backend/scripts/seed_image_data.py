"""Seed script for creating realistic image, point, and embedding data."""

import random
import time
import sys
import os
import numpy as np
from typing import List, Dict, Any

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database import get_db
from server.database.queries import users, images, points, embeddings, reduced_embeddings
from server.database.tables import User


def create_sample_user(db) -> User:
    """Create a sample user for testing."""
    # Check if user already exists
    existing_user = db.query(User).filter(User.username == "testuser").first()
    if existing_user:
        return existing_user
    
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password="$2b$12$dummy_hash",  # Dummy hash for testing
        full_name="Test User",
        is_active=True,
        is_superuser=False,
        created_at=int(time.time() * 1000),
        updated_at=int(time.time() * 1000),
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


def generate_realistic_images(user_id: int, count: int = 50) -> List[Dict[str, Any]]:
    """Generate realistic image metadata."""
    images_data = []
    
    # Define some realistic robot IDs and locations
    robot_ids = ["robot_001", "robot_002", "robot_003", "robot_004"]
    
    # Define geographic bounds (San Francisco Bay Area)
    lat_bounds = (37.7, 37.8)
    lng_bounds = (-122.5, -122.3)
    
    # Generate images over the past 30 days
    current_time = int(time.time() * 1000)
    thirty_days_ago = current_time - (30 * 24 * 60 * 60 * 1000)
    
    for i in range(count):
        # Random timestamp in the past 30 days
        captured_at = random.randint(thirty_days_ago, current_time)
        
        # Random location in SF Bay Area
        latitude = random.uniform(*lat_bounds)
        longitude = random.uniform(*lng_bounds)
        
        # Random image dimensions (typical camera resolutions)
        width_height_pairs = [
            (1920, 1080),  # 1080p
            (1280, 720),   # 720p
            (2560, 1440),  # 1440p
            (3840, 2160),  # 4K
        ]
        width, height = random.choice(width_height_pairs)
        
        # Random robot
        robot_id = random.choice(robot_ids)
        
        images_data.append({
            "user_id": user_id,
            "captured_at": captured_at,
            "latitude": latitude,
            "longitude": longitude,
            "height": height,
            "width": width,
            "robot_id": robot_id,
        })
    
    return images_data


def generate_points_for_image(image_id: int, image_width: int, image_height: int, count: int = None) -> List[Dict[str, Any]]:
    """Generate realistic points for an image."""
    if count is None:
        # Random number of points between 10 and 100
        count = random.randint(10, 100)
    
    points_data = []
    
    for _ in range(count):
        # Random coordinates within image bounds
        x = random.uniform(0, image_width)
        y = random.uniform(0, image_height)
        
        # Random radius between 5 and 50 pixels
        radius = random.uniform(5.0, 50.0)
        
        points_data.append({
            "x": x,
            "y": y,
            "radius": radius,
        })
    
    return points_data


def generate_embeddings_for_point(point_id: int) -> List[Dict[str, Any]]:
    """Generate realistic embeddings for a point."""
    embeddings_data = []
    
    # Different models that might generate embeddings
    models = ["resnet50", "vit_base", "clip_vit", "dino_v2"]
    
    # Generate 1-2 embeddings per point (different models)
    num_embeddings = random.randint(1, 2)
    selected_models = random.sample(models, num_embeddings)
    
    for model_id in selected_models:
        # Generate random embedding vector (512 dimensions)
        dimensions = 512
        vector = np.random.normal(0, 1, dimensions).tolist()
        
        embeddings_data.append({
            "point_id": point_id,
            "model_id": model_id,
            "vector": vector,
            "dimensions": dimensions,
        })
    
    return embeddings_data


def generate_reduced_embeddings_for_embedding(embedding_id: int) -> List[Dict[str, Any]]:
    """Generate reduced embeddings for visualization."""
    reduced_embeddings_data = []
    
    # Generate UMAP 3D reduction
    x = random.uniform(-10, 10)
    y = random.uniform(-10, 10)
    z = random.uniform(-10, 10)
    
    reduced_embeddings_data.append({
        "embedding_id": embedding_id,
        "method": "umap_3d",
        "x": x,
        "y": y,
        "z": z,
        "method_params": {"n_neighbors": 15, "min_dist": 0.1, "n_components": 3},
    })
    
    # Generate t-SNE 2D reduction
    x_2d = random.uniform(-20, 20)
    y_2d = random.uniform(-20, 20)
    
    reduced_embeddings_data.append({
        "embedding_id": embedding_id,
        "method": "tsne_2d",
        "x": x_2d,
        "y": y_2d,
        "z": None,
        "method_params": {"perplexity": 30, "learning_rate": 200},
    })
    
    return reduced_embeddings_data


def main():
    """Main seeding function."""
    print("🌱 Starting image data seeding...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Create sample user
        print("👤 Creating sample user...")
        user = create_sample_user(db)
        print(f"✅ User created: {user.username} (ID: {user.id})")
        
        # Generate and create images
        print("📸 Generating images...")
        images_data = generate_realistic_images(user.id, count=20)
        
        created_images = []
        for image_data in images_data:
            image = images.create_image(db=db, **image_data)
            created_images.append(image)
        
        print(f"✅ Created {len(created_images)} images")
        
        # Generate points for each image
        print("📍 Generating points...")
        total_points = 0
        created_embeddings = []
        
        for image in created_images:
            # Generate points for this image
            points_data = generate_points_for_image(image.id, image.width, image.height)
            
            # Create points in batch
            created_points = points.create_points_batch(db=db, image_id=image.id, points_data=points_data)
            total_points += len(created_points)
            
            # Generate embeddings for each point
            for point in created_points:
                embeddings_data = generate_embeddings_for_point(point.id)
                point_embeddings = embeddings.create_embeddings_batch(db=db, embeddings_data=embeddings_data)
                created_embeddings.extend(point_embeddings)
        
        print(f"✅ Created {total_points} points")
        print(f"✅ Created {len(created_embeddings)} embeddings")
        
        # Generate reduced embeddings for visualization
        print("🎯 Generating reduced embeddings...")
        total_reduced = 0
        
        for embedding in created_embeddings:
            reduced_data = generate_reduced_embeddings_for_embedding(embedding.id)
            reduced_embeddings.create_reduced_embeddings_batch(db=db, reduced_embeddings_data=reduced_data)
            total_reduced += len(reduced_data)
        
        print(f"✅ Created {total_reduced} reduced embeddings")
        
        print("🎉 Image data seeding completed successfully!")
        print(f"📊 Summary:")
        print(f"   - Images: {len(created_images)}")
        print(f"   - Points: {total_points}")
        print(f"   - Embeddings: {len(created_embeddings)}")
        print(f"   - Reduced Embeddings: {total_reduced}")
        
    except Exception as e:
        print(f"❌ Error during seeding: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()
