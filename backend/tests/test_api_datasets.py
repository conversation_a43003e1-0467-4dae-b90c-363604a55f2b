"""Integration tests for dataset API endpoints."""

import pytest
from server.database.queries import users, datasets, datapoints, embeddings


@pytest.fixture
def sample_data(db_session):
    """Create sample data for testing."""
    # Create user
    user = users.create(
        db=db_session,
        username="testuser",
        email="<EMAIL>",
        hashed_password="password123",
        full_name="Test User"
    )

    # Create dataset
    dataset = datasets.create(
        db=db_session,
        name="Test Dataset",
        owner_id=user.id,
        description="A test dataset"
    )
    
    # Create datapoints with embeddings
    for i in range(5):
        datapoint = datapoints.create(
            db=db_session,
            dataset_id=dataset.id,
            label=f"Point_{i+1}",
            data_metadata={"category": "test", "value": i},
            original_index=i
        )

        # Create 3D embedding
        embeddings.create(
            db=db_session,
            datapoint_id=datapoint.id,
            embedding_type="umap_3d",
            dimensions=3,
            vector=[float(i), float(i+1), float(i+2)],
            x=float(i), y=float(i+1), z=float(i+2)
        )

        # Create original embedding
        embeddings.create(
            db=db_session,
            datapoint_id=datapoint.id,
            embedding_type="original",
            dimensions=512,
            vector=[0.1] * 512
        )
    
    return {"user": user, "dataset": dataset}


class TestDatasetEndpoints:
    """Test dataset API endpoints."""
    
    def test_list_datasets(self, client, sample_data):
        """Test listing datasets."""
        response = client.get("/api/v1/datasets/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "datasets" in data
        assert "total" in data
        assert data["total"] == 1
        assert len(data["datasets"]) == 1
        
        dataset = data["datasets"][0]
        assert dataset["name"] == "Test Dataset"
        assert dataset["description"] == "A test dataset"
    
    def test_get_dataset(self, client, sample_data):
        """Test getting a specific dataset."""
        dataset_id = sample_data["dataset"].id
        response = client.get(f"/api/v1/datasets/{dataset_id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == dataset_id
        assert data["name"] == "Test Dataset"
        assert data["description"] == "A test dataset"
    
    def test_get_dataset_not_found(self, client, sample_data):
        """Test getting a non-existent dataset."""
        response = client.get("/api/v1/datasets/999")

        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Dataset not found"
    
    def test_get_dataset_embeddings_3d(self, client, sample_data):
        """Test getting 3D embeddings for a dataset."""
        dataset_id = sample_data["dataset"].id
        response = client.get(f"/api/v1/datasets/{dataset_id}/embeddings/3d?sampling_method=first_n")

        assert response.status_code == 200
        data = response.json()

        assert len(data) == 5

        # Check first embedding (should be Point_1 with coordinates 0,1,2)
        embedding = data[0]
        assert "id" in embedding
        assert "x" in embedding
        assert "y" in embedding
        assert "z" in embedding
        assert "label" in embedding
        assert "metadata" in embedding
        assert embedding["embedding_type"] == "umap_3d"
        assert embedding["dimensions"] == 3
        assert embedding["label"] == "Point_1"

        # Check coordinates are correct (first point should be 0,1,2)
        assert embedding["x"] == 0.0
        assert embedding["y"] == 1.0
        assert embedding["z"] == 2.0
    
    def test_get_dataset_embeddings_3d_with_limit(self, client, sample_data):
        """Test getting 3D embeddings with limit."""
        dataset_id = sample_data["dataset"].id
        response = client.get(f"/api/v1/datasets/{dataset_id}/embeddings/3d?sample_size=3&sampling_method=first_n")

        assert response.status_code == 200
        data = response.json()

        assert len(data) == 3
    
    def test_get_dataset_embeddings_3d_not_found(self, client, sample_data):
        """Test getting 3D embeddings for non-existent dataset."""
        response = client.get("/api/v1/datasets/999/embeddings/3d")

        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Dataset not found"
    
    def test_get_dataset_embeddings_all(self, client, sample_data):
        """Test getting all embeddings for a dataset."""
        dataset_id = sample_data["dataset"].id
        response = client.get(f"/api/v1/datasets/{dataset_id}/embeddings")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["dataset_id"] == dataset_id
        assert data["total_points"] == 10  # 5 datapoints * 2 embeddings each
        assert len(data["embeddings"]) == 10
    
    def test_get_dataset_embeddings_filtered(self, client, sample_data):
        """Test getting embeddings filtered by type."""
        dataset_id = sample_data["dataset"].id
        response = client.get(f"/api/v1/datasets/{dataset_id}/embeddings?embedding_type=umap_3d")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["dataset_id"] == dataset_id
        assert data["embedding_type"] == "umap_3d"
        assert data["total_points"] == 5
        assert len(data["embeddings"]) == 5
        
        # All embeddings should be umap_3d type
        for embedding in data["embeddings"]:
            assert embedding["embedding_type"] == "umap_3d"
    
    def test_get_dataset_info(self, client, sample_data):
        """Test getting dataset info with statistics."""
        dataset_id = sample_data["dataset"].id
        response = client.get(f"/api/v1/datasets/{dataset_id}/info")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "dataset" in data
        assert "embedding_types" in data
        
        dataset_info = data["dataset"]
        assert dataset_info["id"] == dataset_id
        assert dataset_info["name"] == "Test Dataset"
        
        embedding_types = data["embedding_types"]
        assert len(embedding_types) == 2
        
        # Check embedding type statistics
        type_names = [et["type"] for et in embedding_types]
        assert "umap_3d" in type_names
        assert "original" in type_names
        
        # Find umap_3d stats
        umap_stats = next(et for et in embedding_types if et["type"] == "umap_3d")
        assert umap_stats["count"] == 5
        assert umap_stats["bounds"]["x"] == [0.0, 4.0]
        assert umap_stats["bounds"]["y"] == [1.0, 5.0]
        assert umap_stats["bounds"]["z"] == [2.0, 6.0]
