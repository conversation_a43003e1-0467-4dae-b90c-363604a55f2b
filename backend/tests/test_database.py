"""Unit tests for database models and services."""

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from server.database.connection import Base
from server.database.tables import User, Dataset, DataPoint, Embedding
from server.database.queries import users, datasets, datapoints, embeddings


@pytest.fixture
def db_session():
    """Create a test database session."""
    engine = create_engine("sqlite:///:memory:", echo=False)
    Base.metadata.create_all(bind=engine)

    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestingSessionLocal()

    yield session

    session.close()


class TestUserModel:
    """Test User model and UserService."""
    
    def test_create_user(self, db_session):
        """Test creating a user."""
        user = users.create(
            db=db_session,
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password_123",
            full_name="Test User"
        )
        
        assert user.id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.full_name == "Test User"
        assert user.is_active is True
        assert user.is_superuser is False
        assert user.created_at is not None
    
    def test_get_user_by_username(self, db_session):
        """Test getting user by username."""
        # Create user
        users.create(
            db=db_session,
            username="findme",
            email="<EMAIL>",
            hashed_password="password123"
        )

        # Find user
        found_user = users.get_by_username(db_session, "findme")
        assert found_user is not None
        assert found_user.username == "findme"
        
        # Test not found
        not_found = users.get_by_username(db_session, "notexist")
        assert not_found is None


class TestDatasetModel:
    """Test Dataset model and queries."""

    def test_create_dataset(self, db_session):
        """Test creating a dataset."""
        # First create a user
        user = users.create(
            db=db_session,
            username="dataowner",
            email="<EMAIL>",
            hashed_password="password123"
        )

        # Create dataset
        dataset = datasets.create(
            db=db_session,
            name="Test Dataset",
            owner_id=user.id,
            description="A test dataset"
        )
        
        assert dataset.id is not None
        assert dataset.name == "Test Dataset"
        assert dataset.description == "A test dataset"
        assert dataset.owner_id == user.id
        assert dataset.total_points == 0
        assert dataset.status == "uploaded"
    
    def test_get_datasets_by_user(self, db_session):
        """Test getting datasets by user."""
        # Create user
        user = users.create(
            db=db_session,
            username="multiowner",
            email="<EMAIL>",
            hashed_password="password123"
        )

        # Create multiple datasets
        dataset1 = datasets.create(
            db=db_session,
            name="Dataset 1",
            owner_id=user.id
        )
        dataset2 = datasets.create(
            db=db_session,
            name="Dataset 2",
            owner_id=user.id
        )

        # Get datasets
        user_datasets = datasets.get_by_owner(db_session, user.id)
        assert len(user_datasets) == 2
        assert {d.name for d in user_datasets} == {"Dataset 1", "Dataset 2"}


class TestDataPointModel:
    """Test DataPoint model and queries."""

    def test_create_datapoint(self, db_session):
        """Test creating a datapoint."""
        # Setup user and dataset
        user = users.create(
            db=db_session,
            username="pointowner",
            email="<EMAIL>",
            hashed_password="password123"
        )
        dataset = datasets.create(
            db=db_session,
            name="Point Dataset",
            owner_id=user.id
        )

        # Create datapoint
        datapoint = datapoints.create(
            db=db_session,
            dataset_id=dataset.id,
            label="Test Point",
            data_metadata={"category": "test", "value": 42},
            original_index=0
        )
        
        assert datapoint.id is not None
        assert datapoint.dataset_id == dataset.id
        assert datapoint.label == "Test Point"
        assert datapoint.data_metadata == {"category": "test", "value": 42}
        assert datapoint.original_index == 0


class TestEmbeddingModel:
    """Test Embedding model and queries."""

    def test_create_embedding(self, db_session):
        """Test creating an embedding."""
        # Setup user, dataset, and datapoint
        user = users.create(
            db=db_session,
            username="embowner",
            email="<EMAIL>",
            hashed_password="password123"
        )
        dataset = datasets.create(
            db=db_session,
            name="Embedding Dataset",
            owner_id=user.id
        )
        datapoint = datapoints.create(
            db=db_session,
            dataset_id=dataset.id,
            label="Embedding Point"
        )

        # Create embedding
        vector = [0.1, 0.2, 0.3, 0.4, 0.5]
        embedding = embeddings.create(
            db=db_session,
            datapoint_id=datapoint.id,
            embedding_type="original",
            dimensions=5,
            vector=vector,
            method_params={"model": "test"},
            x=1.0, y=2.0, z=3.0
        )
        
        assert embedding.id is not None
        assert embedding.datapoint_id == datapoint.id
        assert embedding.embedding_type == "original"
        assert embedding.dimensions == 5
        assert embedding.vector == vector
        assert embedding.method_params == {"model": "test"}
        assert embedding.x == 1.0
        assert embedding.y == 2.0
        assert embedding.z == 3.0
