"""Unit tests for configuration management."""

import pytest
import os
from server.config import Settings, get_settings


class TestSettings:
    """Test the Settings configuration class."""

    def test_default_settings(self):
        """Test that default settings are properly set."""
        settings = Settings()

        assert settings.app_name == "Loom API"
        assert settings.environment == "development"
        assert settings.host == "0.0.0.0"
        assert settings.port == 8000
        assert settings.workers == 1
        assert settings.db_host == "localhost"
        assert settings.db_port == 5432
        assert settings.db_name == "loom"
        assert settings.algorithm == "HS256"

    def test_environment_detection_methods(self):
        """Test environment detection helper methods."""
        # Test development
        dev_settings = Settings(environment="development")
        assert dev_settings.is_development is True
        assert dev_settings.is_production is False
        assert dev_settings.is_testing is False

        # Test production
        prod_settings = Settings(environment="production")
        assert prod_settings.is_development is False
        assert prod_settings.is_production is True
        assert prod_settings.is_testing is False

        # Test testing
        test_settings = Settings(environment="test")
        assert test_settings.is_development is False
        assert test_settings.is_production is False
        assert test_settings.is_testing is True

    def test_database_url_construction(self):
        """Test database URL construction logic."""
        # Test the property directly by creating a settings object with explicit values
        # and testing the URL construction logic
        settings = Settings()

        # Manually set the attributes to test the property
        settings.db_user = "testuser"
        settings.db_password = "testpass"
        settings.db_host = "testhost"
        settings.db_port = 5433
        settings.db_name = "testdb"
        settings.database_url = None  # Ensure we use the constructed URL

        expected_url = "********************************************/testdb"
        assert settings.database_url_complete == expected_url

    def test_database_url_override(self):
        """Test that explicit database_url takes precedence."""
        custom_url = "***********************************/custom"
        settings = Settings(
            database_url=custom_url, db_user="ignored", db_password="ignored"
        )

        assert settings.database_url_complete == custom_url

    def test_environment_variable_override(self, monkeypatch):
        """Test that environment variables override defaults."""
        monkeypatch.setenv("APP_NAME", "Test App")
        monkeypatch.setenv("PORT", "9000")
        monkeypatch.setenv("DEBUG", "true")

        settings = Settings()

        assert settings.app_name == "Test App"
        assert settings.port == 9000
        assert settings.debug is True

    def test_get_settings_function(self):
        """Test the get_settings function."""
        settings = get_settings()
        assert isinstance(settings, Settings)
        assert settings.app_name == "Loom API"


class TestConfigurationValidation:
    """Test configuration validation and edge cases."""

    def test_cors_origins_list(self):
        """Test CORS origins configuration."""
        settings = Settings(cors_origins="http://localhost:3000,https://example.com")
        origins_list = settings.cors_origins_list
        assert len(origins_list) == 2
        assert "http://localhost:3000" in origins_list
        assert "https://example.com" in origins_list

        # Test single origin
        single_settings = Settings(cors_origins="http://localhost:3000")
        assert single_settings.cors_origins_list == ["http://localhost:3000"]

        # Test wildcard
        wildcard_settings = Settings(cors_origins="*")
        assert wildcard_settings.cors_origins_list == ["*"]

    def test_max_upload_size_default(self):
        """Test default max upload size."""
        settings = Settings()
        assert settings.max_upload_size == 100 * 1024 * 1024  # 100MB

    def test_upload_directory_default(self):
        """Test default upload directory."""
        settings = Settings()
        assert settings.upload_dir == "uploads"
