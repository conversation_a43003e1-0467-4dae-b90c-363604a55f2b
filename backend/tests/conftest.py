"""Pytest configuration and fixtures for Loom backend integration tests."""

import pytest
import os
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from server.main import app
from server.config import Settings, get_settings
from server.database.connection import Base, get_db


def get_test_settings():
    """Get test settings with PostgreSQL test database."""
    # Use environment variables or defaults for test database
    # Use 'postgres' as host when running inside Docker, 'localhost' otherwise
    test_db_host = os.getenv(
        "TEST_DB_HOST", "postgres" if os.getenv("ENVIRONMENT") else "localhost"
    )
    test_db_port = os.getenv("TEST_DB_PORT", "5432")
    test_db_user = os.getenv("TEST_DB_USER", "loom_user")
    test_db_password = os.getenv("TEST_DB_PASSWORD", "loom_password")
    test_db_name = os.getenv("TEST_DB_NAME", "loom_test")

    database_url = f"postgresql://{test_db_user}:{test_db_password}@{test_db_host}:{test_db_port}/{test_db_name}"

    return Settings(
        environment="test",
        debug=True,
        database_url=database_url,
        secret_key="test-secret-key",
        cors_origins="http://localhost:3000",
    )


@pytest.fixture
def test_settings():
    return get_test_settings()


@pytest.fixture(scope="session")
def test_engine():
    """Create a test database engine for the session."""
    settings = get_test_settings()
    engine = create_engine(settings.database_url, echo=False)

    # Create all tables
    Base.metadata.create_all(bind=engine)

    yield engine

    # Clean up: drop all tables
    Base.metadata.drop_all(bind=engine)
    engine.dispose()


@pytest.fixture
def db_session(test_engine):
    """Create a test database session with transaction rollback."""
    connection = test_engine.connect()
    transaction = connection.begin()

    # Create session bound to the connection
    TestingSessionLocal = sessionmaker(
        autocommit=False, autoflush=False, bind=connection
    )
    session = TestingSessionLocal()

    yield session

    # Rollback transaction to clean up test data
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def client(db_session):
    """Create a test client with database dependency override."""

    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_settings] = get_test_settings

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()


@pytest.fixture
def sample_dataset():
    return {
        "name": "test_dataset",
        "description": "A test dataset for unit testing",
        "embeddings": [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6], [0.7, 0.8, 0.9]],
        "labels": ["item1", "item2", "item3"],
    }
