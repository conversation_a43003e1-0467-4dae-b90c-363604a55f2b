"""restructure_to_images_points_embeddings

Revision ID: 003
Revises: 002
Create Date: 2025-01-27 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade to new schema with images, points, embeddings, and reduced_embeddings."""
    
    # Drop old tables (in reverse dependency order)
    op.drop_table('embeddings')
    op.drop_table('datapoints')
    op.drop_table('datasets')
    
    # Create new images table
    op.create_table('images',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('captured_at', sa.BigInteger(), nullable=False),
        sa.Column('latitude', sa.Float(), nullable=True),
        sa.Column('longitude', sa.Float(), nullable=True),
        sa.Column('height', sa.Integer(), nullable=False),
        sa.Column('width', sa.Integer(), nullable=False),
        sa.Column('robot_id', sa.String(length=255), nullable=False),
        sa.Column('file_path', sa.String(length=500), nullable=True),
        sa.Column('file_size', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.BigInteger(), nullable=False),
        sa.Column('updated_at', sa.BigInteger(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_images_id'), 'images', ['id'], unique=False)
    op.create_index(op.f('ix_images_robot_id'), 'images', ['robot_id'], unique=False)
    
    # Create new points table
    op.create_table('points',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('image_id', sa.Integer(), nullable=False),
        sa.Column('x', sa.Float(), nullable=False),
        sa.Column('y', sa.Float(), nullable=False),
        sa.Column('radius', sa.Float(), nullable=False),
        sa.Column('created_at', sa.BigInteger(), nullable=False),
        sa.Column('updated_at', sa.BigInteger(), nullable=False),
        sa.ForeignKeyConstraint(['image_id'], ['images.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_points_id'), 'points', ['id'], unique=False)
    
    # Create new embeddings table
    op.create_table('embeddings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('point_id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.String(length=255), nullable=False),
        sa.Column('vector', sa.JSON(), nullable=False),
        sa.Column('dimensions', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.BigInteger(), nullable=False),
        sa.Column('updated_at', sa.BigInteger(), nullable=False),
        sa.ForeignKeyConstraint(['point_id'], ['points.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_embeddings_id'), 'embeddings', ['id'], unique=False)
    op.create_index(op.f('ix_embeddings_model_id'), 'embeddings', ['model_id'], unique=False)
    
    # Create new reduced_embeddings table
    op.create_table('reduced_embeddings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('embedding_id', sa.Integer(), nullable=False),
        sa.Column('method', sa.String(length=255), nullable=False),
        sa.Column('method_params', sa.JSON(), nullable=True),
        sa.Column('x', sa.Float(), nullable=False),
        sa.Column('y', sa.Float(), nullable=False),
        sa.Column('z', sa.Float(), nullable=True),
        sa.Column('created_at', sa.BigInteger(), nullable=False),
        sa.Column('updated_at', sa.BigInteger(), nullable=False),
        sa.ForeignKeyConstraint(['embedding_id'], ['embeddings.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_reduced_embeddings_id'), 'reduced_embeddings', ['id'], unique=False)
    op.create_index(op.f('ix_reduced_embeddings_method'), 'reduced_embeddings', ['method'], unique=False)


def downgrade() -> None:
    """Downgrade back to old schema (not recommended - data will be lost)."""
    
    # Drop new tables (in reverse dependency order)
    op.drop_table('reduced_embeddings')
    op.drop_table('embeddings')
    op.drop_table('points')
    op.drop_table('images')
    
    # Recreate old tables (basic structure only - data will be lost)
    op.create_table('datasets',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('owner_id', sa.Integer(), nullable=False),
        sa.Column('total_points', sa.Integer(), nullable=False),
        sa.Column('embedding_dimension', sa.Integer(), nullable=True),
        sa.Column('file_path', sa.String(length=500), nullable=True),
        sa.Column('file_size', sa.Integer(), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('processed_at', sa.BigInteger(), nullable=True),
        sa.Column('created_at', sa.BigInteger(), nullable=False),
        sa.Column('updated_at', sa.BigInteger(), nullable=False),
        sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_table('datapoints',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('dataset_id', sa.Integer(), nullable=False),
        sa.Column('label', sa.String(length=500), nullable=True),
        sa.Column('data_metadata', sa.JSON(), nullable=True),
        sa.Column('original_index', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.BigInteger(), nullable=False),
        sa.Column('updated_at', sa.BigInteger(), nullable=False),
        sa.ForeignKeyConstraint(['dataset_id'], ['datasets.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_table('embeddings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('datapoint_id', sa.Integer(), nullable=False),
        sa.Column('embedding_type', sa.String(length=50), nullable=False),
        sa.Column('method_params', sa.JSON(), nullable=True),
        sa.Column('dimensions', sa.Integer(), nullable=False),
        sa.Column('vector', sa.JSON(), nullable=False),
        sa.Column('x', sa.Float(), nullable=True),
        sa.Column('y', sa.Float(), nullable=True),
        sa.Column('z', sa.Float(), nullable=True),
        sa.Column('created_at', sa.BigInteger(), nullable=False),
        sa.Column('updated_at', sa.BigInteger(), nullable=False),
        sa.ForeignKeyConstraint(['datapoint_id'], ['datapoints.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
