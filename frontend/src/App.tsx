import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import DatasetsPage from './pages/DatasetsPage';
import VisualizationPage from './pages/VisualizationPage';
import './App.css';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Navigate to="/datasets" replace />} />
        <Route path="/datasets" element={<DatasetsPage />} />
        <Route path="/visualize/:datasetId" element={<VisualizationPage />} />
        <Route path="*" element={<Navigate to="/datasets" replace />} />
      </Routes>
    </Router>
  );
}

export default App;
