import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import ImagesPage from './pages/ImagesPage';
import VisualizationPage from './pages/VisualizationPage';
import './App.css';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Navigate to="/images" replace />} />
        <Route path="/images" element={<ImagesPage />} />
        <Route path="/visualize" element={<VisualizationPage />} />
        <Route path="*" element={<Navigate to="/images" replace />} />
      </Routes>
    </Router>
  );
}

export default App;
