/**
 * Testing utilities and helpers
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { vi } from 'vitest';

// Mock data for testing
export const mockDataset = {
  id: 1,
  name: 'Test Dataset',
  description: 'A test dataset for unit tests',
  owner_id: 1,
  total_points: 1000,
  embedding_dimension: 512,
  file_path: '/test/path',
  file_size: 1024000,
  status: 'ready',
  created_at: Date.now(),
  updated_at: Date.now(),
  processed_at: Date.now()
};

export const mockEmbeddingPoint = {
  point_id: 1,
  x: 1.5,
  y: 2.3,
  z: -0.8,
  point_x: 100,
  point_y: 150,
  point_radius: 5,
  image_id: 1,
  image_robot_id: 'robot_001',
  image_captured_at: Date.now(),
  image_latitude: 37.7749,
  image_longitude: -122.4194,
  image_height: 1080,
  image_width: 1920,
  model_id: 'resnet50',
  method: 'umap_3d'
};

export const mockDatasetInfo = {
  dataset: mockDataset,
  embedding_types: [
    {
      type: 'umap_3d',
      count: 1000,
      bounds: {
        x: [-5.0, 5.0],
        y: [-3.0, 3.0],
        z: [-2.0, 2.0]
      }
    },
    {
      type: 'original',
      count: 1000,
      bounds: {}
    }
  ]
};

export const mockVisualizationConfig = {
  pointSize: 0.5,
  colorBy: 'robot_id' as const,
  layerType: 'scatterplot' as const,
  showLabels: false
};

export const mockVisualizationResponse = {
  points: [mockEmbeddingPoint],
  total_points: 1,
  method: 'umap_3d',
  model_id: 'resnet50',
  bounds: {
    x: [-2.0, 2.0],
    y: [-3.0, 3.0],
    z: [-1.0, 1.0]
  }
};

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Mock API service
export const mockApiService = {
  getDatasets: vi.fn(),
  getDataset: vi.fn(),
  getEmbeddings3D: vi.fn(),
  getAllEmbeddings: vi.fn(),
  getDatasetInfo: vi.fn(),
  getVisualization3D: vi.fn(),
  getAvailableRobots: vi.fn(),
  getAvailableModels: vi.fn(),
  getAvailableMethods: vi.fn(),
  healthCheck: vi.fn()
};

// Helper to create mock API responses
export const createMockApiResponse = <T,>(data: T): Promise<T> => {
  return Promise.resolve(data);
};

export const createMockApiError = (message: string): Promise<never> => {
  return Promise.reject(new Error(message));
};

// Helper to wait for async operations
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0));

// Mock Deck.gl components
export const mockDeckGL = {
  ScatterplotLayer: vi.fn(() => ({})),
  OrthographicView: vi.fn(() => ({}))
};

// Helper to mock window.ResizeObserver
export const mockResizeObserver = () => {
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
};
