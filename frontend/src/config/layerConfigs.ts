/**
 * Layer-specific configurations for 3D mesh visualization
 */

export interface MeshLayerConfig {
  sphereGeometry: {
    radius: number;
    nlat: number;
    nlong: number;
  };
  wireframe: boolean;
}

export const MESH_CONFIG: MeshLayerConfig = {
  sphereGeometry: {
    radius: 1.0,
    nlat: 16,
    nlong: 16
  },
  wireframe: false
};

export const LAYER_CONFIGS = {
  mesh: MESH_CONFIG
} as const;
