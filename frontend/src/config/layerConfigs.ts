/**
 * Layer-specific configurations for different visualization types
 */

export interface ScatterplotLayerConfig {
  radiusScale: number;
  radiusMinPixels: number;
  radiusMaxPixels: number;
  filled: boolean;
  stroked: boolean;
  strokeWidth: number;
}

export interface MeshLayerConfig {
  sphereGeometry: {
    radius: number;
    nlat: number;
    nlong: number;
  };
  wireframe: boolean;
}

export const SCATTERPLOT_CONFIG: ScatterplotLayerConfig = {
  radiusScale: 100,
  radiusMinPixels: 1,
  radiusMaxPixels: 100,
  filled: true,
  stroked: false,
  strokeWidth: 1
};

export const MESH_CONFIG: MeshLayerConfig = {
  sphereGeometry: {
    radius: 1,
    nlat: 12,
    nlong: 12
  },
  wireframe: false
};

export const LAYER_CONFIGS = {
  scatterplot: SCATTERPLOT_CONFIG,
  mesh: MESH_CONFIG
} as const;
