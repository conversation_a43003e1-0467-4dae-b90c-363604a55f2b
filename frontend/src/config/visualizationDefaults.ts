/**
 * Default configurations for visualization components
 */

import { VisualizationConfig } from '../hooks/useVisualizationConfig';
import { ViewportState } from '../hooks/useViewport';

export const DEFAULT_VISUALIZATION_CONFIG: VisualizationConfig = {
  pointSize: 0.5,
  colorBy: 'robot_id',
  layerType: 'scatterplot',
  showLabels: false
};

export const DEFAULT_VIEWPORT_STATE: ViewportState = {
  target: [0, 0, 0],
  rotationOrbit: 0,
  rotationX: 30,
  zoom: 1,
  minZoom: -10,
  maxZoom: 10,
  minRotationX: -90,
  maxRotationX: 90
};

export const VISUALIZATION_LIMITS = {
  pointSize: {
    min: 0.1,
    max: 2.0,
    step: 0.1
  },
  sampleSize: {
    min: 1000,
    max: 100000,
    default: 50000
  },
  zoom: {
    min: -10,
    max: 10
  }
} as const;
