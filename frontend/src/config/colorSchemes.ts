/**
 * Color mapping configurations for different visualization schemes
 */

import { ColorMapping } from '../types/api';
import { ColorScheme } from '../hooks/useVisualizationConfig';

// Color mappings for different robot types
export const ROBOT_COLORS: ColorMapping = {
  'robot_001': [255, 69, 0],        // Red-Orange
  'robot_002': [50, 205, 50],       // Lime Green
  'robot_003': [0, 191, 255],       // Deep Sky Blue
  'robot_004': [255, 215, 0],       // Gold
  'default': [128, 128, 128]        // Gray
};

// Color mappings for different model types
export const MODEL_COLORS: ColorMapping = {
  'resnet50': [255, 69, 0],         // Red-Orange
  'vit_base': [50, 205, 50],        // Lime Green
  'clip_vit': [0, 191, 255],        // Deep Sky Blue
  'dino_v2': [255, 215, 0],         // Gold
  'default': [128, 128, 128]        // Gray
};

// Default color for fallback
export const DEFAULT_COLOR: [number, number, number] = [128, 128, 128];

/**
 * Get color mapping for a specific color scheme
 */
export function getColorMapping(scheme: ColorScheme): ColorMapping {
  switch (scheme) {
    case 'robot_id':
      return ROBOT_COLORS;
    case 'model_id':
      return MODEL_COLORS;
    case 'default':
    default:
      return { default: DEFAULT_COLOR };
  }
}

/**
 * Get available color schemes with display names
 */
export function getAvailableColorSchemes(): Array<{ value: ColorScheme; label: string }> {
  return [
    { value: 'robot_id', label: 'Robot ID' },
    { value: 'model_id', label: 'Model ID' },
    { value: 'default', label: 'Default' }
  ];
}
