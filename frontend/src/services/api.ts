/**
 * API service for communicating with the Loom backend
 */

import axios from 'axios';
import {
  Image,
  ImageListResponse,
  EmbeddingPoint3D,
  VisualizationResponse,
  PointResponse,
  ImageFilters,
  PointFilters,
  ApiError
} from '../types/api';

// Configure axios with base URL
const api = axios.create({
  baseURL: 'http://localhost:8000/api/v1',
  timeout: 30000, // 30 seconds for large datasets
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.data) {
      throw new Error(error.response.data.detail || error.response.data.message || 'API Error');
    }
    throw new Error(error.message || 'Network Error');
  }
);

export class ApiService {
  /**
   * Get list of all images with optional filtering
   */
  static async getImages(
    skip = 0,
    limit = 100,
    filters?: ImageFilters
  ): Promise<ImageListResponse> {
    const params: any = { skip, limit };

    // Add filters to params
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params[key] = value;
        }
      });
    }

    const response = await api.get<ImageListResponse>('/images/', { params });
    return response.data;
  }

  /**
   * Get a specific image by ID
   */
  static async getImage(imageId: number): Promise<Image> {
    const response = await api.get<Image>(`/images/${imageId}`);
    return response.data;
  }

  /**
   * Get points for a specific image
   */
  static async getImagePoints(
    imageId: number,
    skip = 0,
    limit = 1000,
    filters?: PointFilters
  ): Promise<PointResponse[]> {
    const params: any = { skip, limit };

    // Add filters to params
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params[key] = value;
        }
      });
    }

    const response = await api.get<PointResponse[]>(`/images/${imageId}/points`, { params });
    return response.data;
  }

  /**
   * Get 3D visualization data with filtering support
   */
  static async getVisualization3D(
    options: {
      method: string;
      model_id?: string;
      sample_size?: number;
      image_filters?: ImageFilters;
      point_filters?: PointFilters;
    }
  ): Promise<VisualizationResponse> {
    const {
      method,
      model_id,
      sample_size = 50000,
      image_filters,
      point_filters
    } = options;

    const params: any = {
      method,
      sample_size
    };

    if (model_id) {
      params.model_id = model_id;
    }

    // Add image filters to params
    if (image_filters) {
      Object.entries(image_filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params[key] = value;
        }
      });
    }

    // Add point filters to params
    if (point_filters) {
      Object.entries(point_filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params[key] = value;
        }
      });
    }

    const response = await api.get<VisualizationResponse>(
      '/images/visualization/3d',
      { params }
    );
    return response.data;
  }

  /**
   * Get available robot IDs for filtering
   */
  static async getAvailableRobots(): Promise<string[]> {
    const response = await api.get<string[]>('/images/filters/robots');
    return response.data;
  }

  /**
   * Get available model IDs for filtering
   */
  static async getAvailableModels(): Promise<string[]> {
    const response = await api.get<string[]>('/images/filters/models');
    return response.data;
  }

  /**
   * Get available reduction methods for filtering
   */
  static async getAvailableMethods(): Promise<string[]> {
    const response = await api.get<string[]>('/images/filters/methods');
    return response.data;
  }

  /**
   * Test API connectivity
   */
  static async healthCheck(): Promise<boolean> {
    try {
      await api.get('/images/', { params: { limit: 1 } });
      return true;
    } catch (error) {
      console.error('API health check failed:', error);
      return false;
    }
  }
}

export default ApiService;
