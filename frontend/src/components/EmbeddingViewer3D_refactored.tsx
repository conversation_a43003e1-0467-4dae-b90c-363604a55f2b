/**
 * Refactored 3D embedding visualization component with separated concerns
 */

import React, { useState, useEffect } from 'react';
import { EmbeddingPoint3D, ImageFilters, PointFilters } from '../types/api';
import { useVisualizationData } from '../hooks/useVisualizationData';
import { useVisualizationConfig } from '../hooks/useVisualizationConfig';
import { useViewport } from '../hooks/useViewport';
import { VisualizationCanvas } from './visualization/VisualizationCanvas';
import { VisualizationControls } from './visualization/VisualizationControls';
import { VisualizationStats } from './visualization/VisualizationStats';
import { HoverTooltip } from './visualization/HoverTooltip';
import { PointsTable } from './visualization/PointsTable';
import { usePointsTable } from '../hooks/usePointsTable';

export interface EmbeddingViewer3DProps {
  method: string;
  modelId?: string;
  imageFilters?: ImageFilters;
  pointFilters?: PointFilters;
  className?: string;
}

/**
 * Main 3D embedding visualization component with separated concerns
 */
const EmbeddingViewer3D: React.FC<EmbeddingViewer3DProps> = ({
  method,
  modelId,
  imageFilters,
  pointFilters,
  className = ''
}) => {
  // State for hovered point
  const [hoveredPoint, setHoveredPoint] = useState<EmbeddingPoint3D | null>(null);

  // Custom hooks for data and state management
  const { data, loading, error, refetch } = useVisualizationData({
    method,
    modelId,
    imageFilters,
    pointFilters
  });

  const { config, updateConfig } = useVisualizationConfig();
  const { viewState, setViewState, fitToData } = useViewport();

  // Table state management
  const [tableState, tableActions] = usePointsTable(true); // Start collapsed

  // Fit viewport to data when data changes
  useEffect(() => {
    if (data) {
      fitToData(data);
    }
  }, [data, fitToData]);

  // Loading state
  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading visualization...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center text-red-600">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium mb-2">Error Loading Visualization</h3>
          <p className="text-sm mb-4">{error}</p>
          <button
            onClick={refetch}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // No data state
  if (!data || data.points.length === 0) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center text-gray-500">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <h3 className="text-lg font-medium mb-2">No Data Available</h3>
          <p className="text-sm">No visualization data found for the current filters.</p>
        </div>
      </div>
    );
  }

  // Handle hover from both canvas and table
  const handlePointHover = (point: EmbeddingPoint3D | null) => {
    setHoveredPoint(point);
    tableActions.setHoveredPoint(point);
  };

  // Handle point selection from table
  const handlePointSelect = (point: EmbeddingPoint3D | null) => {
    tableActions.setSelectedPoint(point);
    // Could add logic here to highlight the point in the 3D view
  };

  return (
    <div className={`${className} relative`}>
      {/* Main visualization canvas */}
      <VisualizationCanvas
        data={data}
        config={config}
        viewport={viewState}
        onViewportChange={setViewState}
        onHover={handlePointHover}
        className="w-full h-full"
      />

      {/* Controls overlay */}
      <div className="absolute top-4 left-4 max-w-xs">
        <VisualizationControls
          config={config}
          onConfigChange={updateConfig}
          className="mb-4"
        />

        <VisualizationStats
          data={data}
          className="bg-white bg-opacity-90 rounded-lg p-3 shadow-lg"
        />
      </div>

      {/* Hover tooltip */}
      {hoveredPoint && (
        <HoverTooltip
          point={hoveredPoint}
          className="absolute bottom-4 left-4"
        />
      )}

      {/* Collapsible points table */}
      <PointsTable
        data={data.points}
        isCollapsed={tableState.isCollapsed}
        onToggleCollapse={tableActions.toggleCollapse}
        onPointHover={handlePointHover}
        onPointSelect={handlePointSelect}
      />
    </div>
  );
};

export default EmbeddingViewer3D;
