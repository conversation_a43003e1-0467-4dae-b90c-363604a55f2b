/**
 * Component for displaying visualization statistics and metadata
 */

import React from 'react';
import { VisualizationResponse } from '../../types/api';

export interface VisualizationStatsProps {
  data: VisualizationResponse;
  className?: string;
}

/**
 * Pure component for displaying visualization stats
 */
export const VisualizationStats: React.FC<VisualizationStatsProps> = ({
  data,
  className = ''
}) => {
  return (
    <div className={`text-xs text-gray-500 ${className}`}>
      <div>Points: {data.total_points.toLocaleString()}</div>
      <div>Method: {data.method}</div>
      {data.model_id && <div>Model: {data.model_id}</div>}
      {data.bounds && (
        <div className="mt-2">
          <div className="font-medium">Bounds:</div>
          <div>X: [{data.bounds.x[0].toFixed(2)}, {data.bounds.x[1].toFixed(2)}]</div>
          <div>Y: [{data.bounds.y[0].toFixed(2)}, {data.bounds.y[1].toFixed(2)}]</div>
          <div>Z: [{data.bounds.z[0].toFixed(2)}, {data.bounds.z[1].toFixed(2)}]</div>
        </div>
      )}
    </div>
  );
};
