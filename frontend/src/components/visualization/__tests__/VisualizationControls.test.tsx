/**
 * Tests for VisualizationControls component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '../../../test/utils';
import { VisualizationControls } from '../VisualizationControls';

const mockVisualizationConfig = {
  pointSize: 0.5,
  colorBy: 'robot_id' as const,
  layerType: 'scatterplot' as const,
  showLabels: false
};

describe('VisualizationControls', () => {
  const mockOnConfigChange = vi.fn();

  beforeEach(() => {
    mockOnConfigChange.mockClear();
  });

  it('should render all control elements', () => {
    render(
      <VisualizationControls
        config={mockVisualizationConfig}
        onConfigChange={mockOnConfigChange}
      />
    );

    expect(screen.getByTestId('point-size-control')).toBeInTheDocument();
    expect(screen.getByTestId('color-scheme-control')).toBeInTheDocument();
    expect(screen.getByTestId('layer-type-control')).toBeInTheDocument();
    expect(screen.getByTestId('labels-control')).toBeInTheDocument();
    expect(screen.getByText('Visualization Controls')).toBeInTheDocument();
  });

  it('should display current point size value', () => {
    const config = { ...mockVisualizationConfig, pointSize: 0.5 };
    render(
      <VisualizationControls
        config={config}
        onConfigChange={mockOnConfigChange}
      />
    );

    expect(screen.getByText('Point Size: 0.5')).toBeInTheDocument();
  });

  it('should handle point size slider changes', () => {
    render(
      <VisualizationControls
        config={mockVisualizationConfig}
        onConfigChange={mockOnConfigChange}
      />
    );

    const slider = screen.getByTestId('point-size-slider');
    fireEvent.change(slider, { target: { value: '1.2' } });

    expect(mockOnConfigChange).toHaveBeenCalledWith({
      pointSize: 1.2
    });
  });

  it('should handle color scheme changes', () => {
    render(
      <VisualizationControls
        config={mockVisualizationConfig}
        onConfigChange={mockOnConfigChange}
      />
    );

    const select = screen.getByTestId('color-scheme-select');
    fireEvent.change(select, { target: { value: 'model_id' } });

    expect(mockOnConfigChange).toHaveBeenCalledWith({
      colorBy: 'model_id'
    });
  });

  it('should handle labels toggle', () => {
    render(
      <VisualizationControls
        config={mockVisualizationConfig}
        onConfigChange={mockOnConfigChange}
      />
    );

    const checkbox = screen.getByTestId('labels-checkbox');
    fireEvent.click(checkbox);

    expect(mockOnConfigChange).toHaveBeenCalledWith({
      showLabels: true
    });
  });

  it('should handle layer type changes', () => {
    render(
      <VisualizationControls
        config={mockVisualizationConfig}
        onConfigChange={mockOnConfigChange}
      />
    );

    const select = screen.getByTestId('layer-type-select');
    fireEvent.change(select, { target: { value: 'mesh' } });

    expect(mockOnConfigChange).toHaveBeenCalledWith({
      layerType: 'mesh'
    });
  });

  it('should display layer type options', () => {
    render(
      <VisualizationControls
        config={mockVisualizationConfig}
        onConfigChange={mockOnConfigChange}
      />
    );

    expect(screen.getByText('2D Circles')).toBeInTheDocument();
    expect(screen.getByText('3D Spheres')).toBeInTheDocument();
  });

  it('should have correct slider attributes', () => {
    render(
      <VisualizationControls
        config={mockVisualizationConfig}
        onConfigChange={mockOnConfigChange}
      />
    );

    const slider = screen.getByTestId('point-size-slider');
    expect(slider).toHaveAttribute('type', 'range');
    expect(slider).toHaveAttribute('min', '0.1');
    expect(slider).toHaveAttribute('max', '2');
    expect(slider).toHaveAttribute('step', '0.1');
    expect(slider).toHaveAttribute('value', '0.5');
  });

  it('should display all color scheme options', () => {
    render(
      <VisualizationControls
        config={mockVisualizationConfig}
        onConfigChange={mockOnConfigChange}
      />
    );

    expect(screen.getByText('Robot ID')).toBeInTheDocument();
    expect(screen.getByText('Model ID')).toBeInTheDocument();
    expect(screen.getByText('Default')).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    const customClass = 'custom-controls-class';
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange}
        className={customClass}
      />
    );
    
    const container = screen.getByText('Visualization Controls').parentElement;
    expect(container).toHaveClass(customClass);
  });

  it('should reflect current config values in form elements', () => {
    const config = {
      pointSize: 1.2,
      colorBy: 'model_id' as const,
      layerType: 'mesh' as const,
      showLabels: true
    };

    render(
      <VisualizationControls
        config={config}
        onConfigChange={mockOnConfigChange}
      />
    );

    expect(screen.getByTestId('point-size-slider')).toHaveAttribute('value', '1.2');
    expect(screen.getByTestId('color-scheme-select')).toHaveValue('model_id');
    expect(screen.getByTestId('layer-type-select')).toHaveValue('mesh');
    expect(screen.getByTestId('labels-checkbox')).toBeChecked();
  });
});
