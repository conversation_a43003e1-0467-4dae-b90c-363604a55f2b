/**
 * Tests for VisualizationCanvas component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render } from '../../../test/utils';
import { VisualizationCanvas } from '../VisualizationCanvas';

// Mock DeckGL
vi.mock('@deck.gl/react', () => ({
  default: vi.fn(({ layers, onViewStateChange, viewState }) => (
    <div data-testid="deck-gl-canvas">
      <div data-testid="layers-count">{layers.length}</div>
      <button 
        data-testid="viewport-change-trigger"
        onClick={() => onViewStateChange({ viewState: { ...viewState, zoom: 2 } })}
      >
        Change Viewport
      </button>
    </div>
  ))
}));

// Mock OrbitView
vi.mock('@deck.gl/core', () => ({
  OrbitView: vi.fn().mockImplementation(() => ({ type: 'orbit' }))
}));

// Mock LayerFactory
vi.mock('../layers/LayerFactory', () => ({
  LayerFactory: {
    createPointsLayer: vi.fn().mockReturnValue({ id: 'test-layer' })
  }
}));

const mockData = {
  points: [
    {
      point_id: 1,
      x: 1.5,
      y: 2.3,
      z: -0.8,
      point_x: 100,
      point_y: 150,
      point_radius: 5,
      image_id: 1,
      image_robot_id: 'robot_001',
      image_captured_at: Date.now(),
      image_latitude: 37.7749,
      image_longitude: -122.4194,
      image_height: 1080,
      image_width: 1920,
      model_id: 'resnet50',
      method: 'umap_3d'
    }
  ],
  total_points: 1,
  method: 'umap_3d',
  model_id: 'resnet50',
  bounds: {
    x: [-2.0, 2.0],
    y: [-3.0, 3.0],
    z: [-1.0, 1.0]
  }
};

const mockConfig = {
  pointSize: 0.5,
  colorBy: 'robot_id' as const,
  layerType: 'scatterplot' as const,
  showLabels: false
};

const mockViewport = {
  target: [0, 0, 0] as [number, number, number],
  rotationOrbit: 0,
  rotationX: 30,
  zoom: 1,
  minZoom: -10,
  maxZoom: 10,
  minRotationX: -90,
  maxRotationX: 90
};

describe('VisualizationCanvas', () => {
  const mockOnViewportChange = vi.fn();
  const mockOnHover = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render DeckGL canvas', () => {
    const { getByTestId } = render(
      <VisualizationCanvas
        data={mockData}
        config={mockConfig}
        viewport={mockViewport}
        onViewportChange={mockOnViewportChange}
        onHover={mockOnHover}
      />
    );

    expect(getByTestId('deck-gl-canvas')).toBeInTheDocument();
  });

  it('should create layers when data is provided', () => {
    const { getByTestId } = render(
      <VisualizationCanvas
        data={mockData}
        config={mockConfig}
        viewport={mockViewport}
        onViewportChange={mockOnViewportChange}
        onHover={mockOnHover}
      />
    );

    expect(getByTestId('layers-count')).toHaveTextContent('1');
  });

  it('should not create layers when data is empty', () => {
    const emptyData = { ...mockData, points: [] };
    
    const { getByTestId } = render(
      <VisualizationCanvas
        data={emptyData}
        config={mockConfig}
        viewport={mockViewport}
        onViewportChange={mockOnViewportChange}
        onHover={mockOnHover}
      />
    );

    expect(getByTestId('layers-count')).toHaveTextContent('0');
  });

  it('should handle viewport changes', () => {
    const { getByTestId } = render(
      <VisualizationCanvas
        data={mockData}
        config={mockConfig}
        viewport={mockViewport}
        onViewportChange={mockOnViewportChange}
        onHover={mockOnHover}
      />
    );

    const trigger = getByTestId('viewport-change-trigger');
    trigger.click();

    expect(mockOnViewportChange).toHaveBeenCalledWith(
      expect.objectContaining({
        zoom: 2
      })
    );
  });

  it('should apply custom className', () => {
    const { container } = render(
      <VisualizationCanvas
        data={mockData}
        config={mockConfig}
        viewport={mockViewport}
        onViewportChange={mockOnViewportChange}
        onHover={mockOnHover}
        className="custom-canvas-class"
      />
    );

    expect(container.firstChild).toHaveClass('custom-canvas-class');
  });

  // Note: Layer creation error handling and config change testing
  // are implementation details that are better tested through integration tests
});
