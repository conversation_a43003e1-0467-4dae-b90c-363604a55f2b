/**
 * Collapsible table component for displaying and filtering point data
 */

import React, { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  createColumnHelper,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
} from '@tanstack/react-table';
import { EmbeddingPoint3D } from '../../types/api';

interface PointsTableProps {
  data: EmbeddingPoint3D[];
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onPointHover?: (point: EmbeddingPoint3D | null) => void;
  onPointSelect?: (point: EmbeddingPoint3D | null) => void;
}

const columnHelper = createColumnHelper<EmbeddingPoint3D>();

export const PointsTable: React.FC<PointsTableProps> = ({
  data,
  isCollapsed,
  onToggleCollapse,
  onPointHover,
  onPointSelect,
}) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  // Define table columns
  const columns = useMemo(
    () => [
      columnHelper.accessor('point_id', {
        header: 'Point ID',
        cell: (info) => info.getValue(),
        size: 80,
      }),
      columnHelper.accessor('x', {
        header: 'X',
        cell: (info) => info.getValue().toFixed(3),
        size: 80,
      }),
      columnHelper.accessor('y', {
        header: 'Y',
        cell: (info) => info.getValue().toFixed(3),
        size: 80,
      }),
      columnHelper.accessor('z', {
        header: 'Z',
        cell: (info) => info.getValue()?.toFixed(3) || 'N/A',
        size: 80,
      }),
      columnHelper.accessor('point_x', {
        header: 'Image X',
        cell: (info) => info.getValue(),
        size: 80,
      }),
      columnHelper.accessor('point_y', {
        header: 'Image Y',
        cell: (info) => info.getValue(),
        size: 80,
      }),
      columnHelper.accessor('point_radius', {
        header: 'Radius',
        cell: (info) => info.getValue(),
        size: 80,
      }),
      columnHelper.accessor('image_id', {
        header: 'Image ID',
        cell: (info) => info.getValue(),
        size: 80,
      }),
      columnHelper.accessor('image_robot_id', {
        header: 'Robot ID',
        cell: (info) => info.getValue(),
        size: 120,
      }),
      columnHelper.accessor('image_captured_at', {
        header: 'Captured At',
        cell: (info) => new Date(info.getValue()).toLocaleString(),
        size: 150,
      }),
      columnHelper.accessor('image_latitude', {
        header: 'Latitude',
        cell: (info) => info.getValue().toFixed(6),
        size: 100,
      }),
      columnHelper.accessor('image_longitude', {
        header: 'Longitude',
        cell: (info) => info.getValue().toFixed(6),
        size: 100,
      }),
      columnHelper.accessor('image_height', {
        header: 'Image Height',
        cell: (info) => info.getValue(),
        size: 100,
      }),
      columnHelper.accessor('image_width', {
        header: 'Image Width',
        cell: (info) => info.getValue(),
        size: 100,
      }),
      columnHelper.accessor('model_id', {
        header: 'Model ID',
        cell: (info) => info.getValue(),
        size: 120,
      }),
      columnHelper.accessor('method', {
        header: 'Method',
        cell: (info) => info.getValue(),
        size: 100,
      }),
    ],
    []
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  });

  const handleRowHover = (point: EmbeddingPoint3D | null) => {
    onPointHover?.(point);
  };

  const handleRowClick = (point: EmbeddingPoint3D) => {
    onPointSelect?.(point);
  };

  // Debug logging
  console.log('PointsTable Debug:', {
    dataLength: data.length,
    isCollapsed,
    tableRowCount: table.getRowModel().rows.length
  });

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-300 shadow-lg z-10">
      {/* Collapse/Expand Header */}
      <div
        className="flex items-center justify-between p-3 bg-gray-50 cursor-pointer hover:bg-gray-100"
        onClick={onToggleCollapse}
      >
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold">Point Data</h3>
          <span className="text-sm text-gray-500">({data.length} points)</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            {isCollapsed ? 'Click to expand' : 'Click to collapse'}
          </span>
          <svg
            className={`w-5 h-5 transform transition-transform ${
              isCollapsed ? 'rotate-180' : ''
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </div>

      {/* Table Content */}
      {!isCollapsed && (
        <div className="max-h-96 overflow-auto">
          {/* Column Visibility Controls */}
          <div className="p-3 bg-gray-50 border-b">
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-gray-700 mr-2">Show columns:</span>
              {table.getAllLeafColumns().map((column) => (
                <label key={column.id} className="flex items-center space-x-1 text-sm">
                  <input
                    type="checkbox"
                    checked={column.getIsVisible()}
                    onChange={column.getToggleVisibilityHandler()}
                    className="rounded"
                  />
                  <span>{column.columnDef.header as string}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="bg-gray-100 sticky top-0">
                {table.getHeaderGroups().map((headerGroup) => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <th
                        key={header.id}
                        className="px-3 py-2 text-left font-medium text-gray-700 border-b cursor-pointer hover:bg-gray-200"
                        style={{ width: header.getSize() }}
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        <div className="flex items-center space-x-1">
                          {flexRender(header.column.columnDef.header, header.getContext())}
                          {{
                            asc: ' ↑',
                            desc: ' ↓',
                          }[header.column.getIsSorted() as string] ?? null}
                        </div>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody>
                {table.getRowModel().rows.map((row) => (
                  <tr
                    key={row.id}
                    className="hover:bg-blue-50 cursor-pointer border-b"
                    onMouseEnter={() => handleRowHover(row.original)}
                    onMouseLeave={() => handleRowHover(null)}
                    onClick={() => handleRowClick(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className="px-3 py-2 text-gray-900">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between p-3 bg-gray-50 border-t">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
                className="px-2 py-1 text-sm border rounded disabled:opacity-50"
              >
                {'<<'}
              </button>
              <button
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
                className="px-2 py-1 text-sm border rounded disabled:opacity-50"
              >
                {'<'}
              </button>
              <button
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
                className="px-2 py-1 text-sm border rounded disabled:opacity-50"
              >
                {'>'}
              </button>
              <button
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
                className="px-2 py-1 text-sm border rounded disabled:opacity-50"
              >
                {'>>'}
              </button>
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <span>
                Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
              </span>
              <select
                value={table.getState().pagination.pageSize}
                onChange={(e) => table.setPageSize(Number(e.target.value))}
                className="border rounded px-2 py-1"
              >
                {[25, 50, 100, 200].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    Show {pageSize}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
