/**
 * Controls panel for 3D visualization
 */

import React from 'react';
import { VisualizationConfig, ColorScheme, LayerType } from '../../hooks/useVisualizationConfig';
import { getAvailableColorSchemes } from '../../config/colorSchemes';
import { VISUALIZATION_LIMITS } from '../../config/visualizationDefaults';

interface VisualizationControlsProps {
  config: VisualizationConfig;
  onConfigChange: (updates: Partial<VisualizationConfig>) => void;
  className?: string;
}

const VisualizationControls: React.FC<VisualizationControlsProps> = ({
  config,
  onConfigChange,
  className = ''
}) => {
  const colorSchemes = getAvailableColorSchemes();

  const handlePointSizeChange = (pointSize: number) => {
    onConfigChange({ pointSize });
  };

  const handleColorSchemeChange = (colorBy: ColorScheme) => {
    onConfigChange({ colorBy });
  };

  const handleLayerTypeChange = (layerType: LayerType) => {
    onConfigChange({ layerType });
  };

  const handleLabelsToggle = (showLabels: boolean) => {
    onConfigChange({ showLabels });
  };

  return (
    <div className={`bg-white bg-opacity-90 rounded-lg p-4 shadow-lg ${className}`}>
      <h3 className="font-semibold mb-3 text-gray-900">Visualization Controls</h3>
      
      <div className="space-y-3">
        {/* Point Size Control */}
        <div data-testid="point-size-control">
          <label className="block text-sm font-medium mb-1">
            Point Size: {config.pointSize.toFixed(1)}
          </label>
          <input
            type="range"
            min={VISUALIZATION_LIMITS.pointSize.min}
            max={VISUALIZATION_LIMITS.pointSize.max}
            step={VISUALIZATION_LIMITS.pointSize.step}
            value={config.pointSize}
            onChange={(e) => handlePointSizeChange(parseFloat(e.target.value))}
            className="w-full"
            data-testid="point-size-slider"
          />
        </div>

        {/* Color Scheme Control */}
        <div data-testid="color-scheme-control">
          <label className="block text-sm font-medium mb-1">Color By</label>
          <select
            value={config.colorBy}
            onChange={(e) => handleColorSchemeChange(e.target.value as ColorScheme)}
            className="w-full p-1 border rounded text-sm"
            data-testid="color-scheme-select"
          >
            {colorSchemes.map(scheme => (
              <option key={scheme.value} value={scheme.value}>
                {scheme.label}
              </option>
            ))}
          </select>
        </div>

        {/* Layer Type Control */}
        <div data-testid="layer-type-control">
          <label className="block text-sm font-medium mb-1">Render Type</label>
          <select
            value={config.layerType}
            onChange={(e) => handleLayerTypeChange(e.target.value as LayerType)}
            className="w-full p-1 border rounded text-sm"
            data-testid="layer-type-select"
          >
            <option value="scatterplot">2D Circles</option>
            <option value="mesh">3D Spheres</option>
          </select>
        </div>

        {/* Labels Toggle */}
        <div data-testid="labels-control">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.showLabels}
              onChange={(e) => handleLabelsToggle(e.target.checked)}
              className="rounded"
              data-testid="labels-checkbox"
            />
            <span className="text-sm font-medium">Show Labels</span>
          </label>
        </div>
      </div>
    </div>
  );
};

export { VisualizationControls };
export default VisualizationControls;
