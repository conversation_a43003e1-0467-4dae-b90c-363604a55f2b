/**
 * Tests for LayerFactory
 */

import { describe, it, expect, vi } from 'vitest';
import { LayerFactory } from '../LayerFactory';
import { ScatterplotLayer } from '@deck.gl/layers';
import { SimpleMeshLayer } from '@deck.gl/mesh-layers';

// Mock Deck.gl layers
vi.mock('@deck.gl/layers', () => ({
  ScatterplotLayer: vi.fn().mockImplementation(() => ({ id: 'scatterplot-layer' }))
}));

vi.mock('@deck.gl/mesh-layers', () => ({
  SimpleMeshLayer: vi.fn().mockImplementation(() => ({ id: 'mesh-layer' }))
}));

vi.mock('@luma.gl/engine', () => ({
  SphereGeometry: vi.fn().mockImplementation(() => ({ type: 'sphere' }))
}));

const mockData = [
  {
    point_id: 1,
    x: 1.5,
    y: 2.3,
    z: -0.8,
    point_x: 100,
    point_y: 150,
    point_radius: 5,
    image_id: 1,
    image_robot_id: 'robot_001',
    image_captured_at: Date.now(),
    image_latitude: 37.7749,
    image_longitude: -122.4194,
    image_height: 1080,
    image_width: 1920,
    model_id: 'resnet50',
    method: 'umap_3d'
  }
];

const mockConfig = {
  pointSize: 0.5,
  colorBy: 'robot_id' as const,
  layerType: 'scatterplot' as const,
  showLabels: false
};

describe('LayerFactory', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should create a ScatterplotLayer for scatterplot type', () => {
    const layer = LayerFactory.createPointsLayer('scatterplot', {
      data: mockData,
      config: mockConfig
    });

    expect(ScatterplotLayer).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 'embedding-points-scatterplot',
        data: mockData,
        pickable: true
      })
    );
  });

  it('should create a SimpleMeshLayer for mesh type', () => {
    const meshConfig = { ...mockConfig, layerType: 'mesh' as const };
    
    const layer = LayerFactory.createPointsLayer('mesh', {
      data: mockData,
      config: meshConfig
    });

    expect(SimpleMeshLayer).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 'embedding-points-mesh',
        data: mockData,
        pickable: true
      })
    );
  });

  it('should throw error for unknown layer type', () => {
    expect(() => {
      LayerFactory.createPointsLayer('unknown' as any, {
        data: mockData,
        config: mockConfig
      });
    }).toThrow('Unknown layer type: unknown');
  });

  it('should pass onHover callback to layer', () => {
    const mockOnHover = vi.fn();
    
    LayerFactory.createPointsLayer('scatterplot', {
      data: mockData,
      config: mockConfig,
      onHover: mockOnHover
    });

    expect(ScatterplotLayer).toHaveBeenCalledWith(
      expect.objectContaining({
        onHover: mockOnHover
      })
    );
  });

  it('should pass updateTriggers to layer', () => {
    const mockUpdateTriggers = { customTrigger: 'value' };
    
    LayerFactory.createPointsLayer('scatterplot', {
      data: mockData,
      config: mockConfig,
      updateTriggers: mockUpdateTriggers
    });

    expect(ScatterplotLayer).toHaveBeenCalledWith(
      expect.objectContaining({
        updateTriggers: expect.objectContaining({
          customTrigger: 'value'
        })
      })
    );
  });

  it('should configure scatterplot layer with correct properties', () => {
    LayerFactory.createPointsLayer('scatterplot', {
      data: mockData,
      config: mockConfig
    });

    const callArgs = vi.mocked(ScatterplotLayer).mock.calls[0][0];
    
    expect(callArgs).toMatchObject({
      id: 'embedding-points-scatterplot',
      data: mockData,
      radiusMinPixels: 1,
      radiusMaxPixels: 100,
      filled: true,
      stroked: false,
      strokeWidth: 1,
      pickable: true
    });

    // Check that functions are provided
    expect(typeof callArgs.getPosition).toBe('function');
    expect(typeof callArgs.getColor).toBe('function');
    expect(typeof callArgs.getRadius).toBe('function');
  });

  it('should configure mesh layer with correct properties', () => {
    const meshConfig = { ...mockConfig, layerType: 'mesh' as const };
    
    LayerFactory.createPointsLayer('mesh', {
      data: mockData,
      config: meshConfig
    });

    const callArgs = vi.mocked(SimpleMeshLayer).mock.calls[0][0];
    
    expect(callArgs).toMatchObject({
      id: 'embedding-points-mesh',
      data: mockData,
      wireframe: false,
      pickable: true
    });

    // Check that functions are provided
    expect(typeof callArgs.getPosition).toBe('function');
    expect(typeof callArgs.getColor).toBe('function');
    expect(typeof callArgs.getScale).toBe('function');
  });
});
