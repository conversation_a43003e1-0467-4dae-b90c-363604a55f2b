/**
 * Factory for creating different types of Deck.gl layers
 */

import { Layer } from '@deck.gl/core';
import { SimpleMeshLayer } from '@deck.gl/mesh-layers';
import { LineLayer } from '@deck.gl/layers';
import { SphereGeometry } from '@luma.gl/engine';
import { EmbeddingPoint3D } from '../../../types/api';
import { VisualizationConfig } from '../../../hooks/useVisualizationConfig';
import { LAYER_CONFIGS } from '../../../config/layerConfigs';
import { getColorMapping } from '../../../config/colorSchemes';

export type LayerType = 'mesh';

export interface LayerFactoryOptions {
  data: EmbeddingPoint3D[];
  config: VisualizationConfig;
  onHover?: (info: any) => void;
  updateTriggers?: Record<string, any>;
}

/**
 * Factory class for creating visualization layers
 */
export class LayerFactory {
  /**
   * Create a points layer (always mesh/3D spheres)
   */
  static createPointsLayer(
    type: LayerType,
    options: LayerFactoryOptions
  ): Layer {
    if (type !== 'mesh') {
      throw new Error(`Unknown layer type: ${type}`);
    }
    return LayerFactory.createMeshLayer(options);
  }



  /**
   * Create a SimpleMeshLayer for 3D sphere rendering
   */
  private static createMeshLayer(options: LayerFactoryOptions): SimpleMeshLayer {
    const { data, config, onHover, updateTriggers } = options;
    const layerConfig = LAYER_CONFIGS.mesh;
    const colorMapping = getColorMapping(config.colorBy);

    // Create sphere geometry
    const sphereGeometry = new SphereGeometry({
      radius: layerConfig.sphereGeometry.radius,
      nlat: layerConfig.sphereGeometry.nlat,
      nlong: layerConfig.sphereGeometry.nlong
    });

    return new SimpleMeshLayer({
      id: 'embedding-points-mesh',
      data,
      mesh: sphereGeometry,
      getPosition: (d: EmbeddingPoint3D) => [d.x, d.y, d.z || 0],
      getColor: (d: EmbeddingPoint3D) => LayerFactory.getPointColor(d, config.colorBy, colorMapping),
      getScale: (d: EmbeddingPoint3D) => [config.pointSize * 0.1, config.pointSize * 0.1, config.pointSize * 0.1],
      sizeScale: 1,
      wireframe: layerConfig.wireframe,
      pickable: true,
      onHover,
      updateTriggers: {
        getColor: [config.colorBy],
        getScale: [config.pointSize],
        ...updateTriggers
      }
    });
  }

  /**
   * Create a 3D grid layer
   */
  static createGridLayer(bounds: {
    x: [number, number];
    y: [number, number];
    z: [number, number];
  }): Layer[] {
    const gridLines: Array<{
      sourcePosition: [number, number, number];
      targetPosition: [number, number, number];
    }> = [];

    const { x: [minX, maxX], y: [minY, maxY], z: [minZ, maxZ] } = bounds;

    // Grid spacing - adjust based on data range
    const xRange = maxX - minX;
    const yRange = maxY - minY;
    const zRange = maxZ - minZ;

    const gridSpacing = Math.max(xRange, yRange, zRange) / 10; // 10 grid lines per dimension

    // Create grid lines for XY plane (at minZ)
    for (let x = minX; x <= maxX; x += gridSpacing) {
      gridLines.push({
        sourcePosition: [x, minY, minZ],
        targetPosition: [x, maxY, minZ]
      });
    }

    for (let y = minY; y <= maxY; y += gridSpacing) {
      gridLines.push({
        sourcePosition: [minX, y, minZ],
        targetPosition: [maxX, y, minZ]
      });
    }

    // Create vertical grid lines (Z direction)
    for (let x = minX; x <= maxX; x += gridSpacing) {
      gridLines.push({
        sourcePosition: [x, minY, minZ],
        targetPosition: [x, minY, maxZ]
      });
    }

    for (let y = minY; y <= maxY; y += gridSpacing) {
      gridLines.push({
        sourcePosition: [minX, y, minZ],
        targetPosition: [minX, y, maxZ]
      });
    }

    return [
      new LineLayer({
        id: 'grid-layer',
        data: gridLines,
        getSourcePosition: (d) => d.sourcePosition,
        getTargetPosition: (d) => d.targetPosition,
        getColor: [200, 200, 200, 100], // Light gray with transparency
        getWidth: 1,
        pickable: false
      })
    ];
  }

  /**
   * Get color for a point based on the color scheme
   */
  private static getPointColor(
    point: EmbeddingPoint3D,
    colorBy: VisualizationConfig['colorBy'],
    colorMapping: Record<string, [number, number, number]>
  ): [number, number, number] {
    let key: string;

    switch (colorBy) {
      case 'robot_id':
        key = point.image_robot_id;
        break;
      case 'model_id':
        key = point.model_id;
        break;
      case 'default':
      default:
        key = 'default';
        break;
    }

    return colorMapping[key] || colorMapping.default || [128, 128, 128];
  }
}
