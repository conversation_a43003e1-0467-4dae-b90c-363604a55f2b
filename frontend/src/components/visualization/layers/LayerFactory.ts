/**
 * Factory for creating different types of Deck.gl layers
 */

import { Layer } from '@deck.gl/core';
import { ScatterplotLayer } from '@deck.gl/layers';
import { SimpleMeshLayer } from '@deck.gl/mesh-layers';
import { SphereGeometry } from '@luma.gl/engine';
import { EmbeddingPoint3D } from '../../../types/api';
import { VisualizationConfig } from '../../../hooks/useVisualizationConfig';
import { LAYER_CONFIGS } from '../../../config/layerConfigs';
import { getColorMapping } from '../../../config/colorSchemes';

export type LayerType = 'scatterplot' | 'mesh';

export interface LayerFactoryOptions {
  data: EmbeddingPoint3D[];
  config: VisualizationConfig;
  onHover?: (info: any) => void;
  updateTriggers?: Record<string, any>;
}

/**
 * Factory class for creating visualization layers
 */
export class LayerFactory {
  /**
   * Create a points layer based on the specified type
   */
  static createPointsLayer(
    type: LayerType,
    options: LayerFactoryOptions
  ): Layer {
    switch (type) {
      case 'scatterplot':
        return LayerFactory.createScatterplotLayer(options);
      case 'mesh':
        return LayerFactory.createMeshLayer(options);
      default:
        throw new Error(`Unknown layer type: ${type}`);
    }
  }

  /**
   * Create a ScatterplotLayer for 2D circle rendering
   */
  private static createScatterplotLayer(options: LayerFactoryOptions): ScatterplotLayer {
    const { data, config, onHover, updateTriggers } = options;
    const layerConfig = LAYER_CONFIGS.scatterplot;
    const colorMapping = getColorMapping(config.colorBy);

    return new ScatterplotLayer({
      id: 'embedding-points-scatterplot',
      data,
      getPosition: (d: EmbeddingPoint3D) => [d.x, d.y, d.z || 0],
      getColor: (d: EmbeddingPoint3D) => LayerFactory.getPointColor(d, config.colorBy, colorMapping),
      getRadius: () => config.pointSize * layerConfig.radiusScale,
      radiusMinPixels: layerConfig.radiusMinPixels,
      radiusMaxPixels: layerConfig.radiusMaxPixels,
      filled: layerConfig.filled,
      stroked: layerConfig.stroked,
      strokeWidth: layerConfig.strokeWidth,
      pickable: true,
      onHover,
      updateTriggers: {
        getColor: [config.colorBy],
        getRadius: [config.pointSize],
        ...updateTriggers
      }
    });
  }

  /**
   * Create a SimpleMeshLayer for 3D sphere rendering
   */
  private static createMeshLayer(options: LayerFactoryOptions): SimpleMeshLayer {
    const { data, config, onHover, updateTriggers } = options;
    const layerConfig = LAYER_CONFIGS.mesh;
    const colorMapping = getColorMapping(config.colorBy);

    // Create sphere geometry
    const sphereGeometry = new SphereGeometry({
      radius: layerConfig.sphereGeometry.radius,
      nlat: layerConfig.sphereGeometry.nlat,
      nlong: layerConfig.sphereGeometry.nlong
    });

    return new SimpleMeshLayer({
      id: 'embedding-points-mesh',
      data,
      mesh: sphereGeometry,
      getPosition: (d: EmbeddingPoint3D) => [d.x, d.y, d.z || 0],
      getColor: (d: EmbeddingPoint3D) => LayerFactory.getPointColor(d, config.colorBy, colorMapping),
      getScale: (d: EmbeddingPoint3D) => [config.pointSize * 0.1, config.pointSize * 0.1, config.pointSize * 0.1],
      sizeScale: 1,
      wireframe: layerConfig.wireframe,
      pickable: true,
      onHover,
      updateTriggers: {
        getColor: [config.colorBy],
        getScale: [config.pointSize],
        ...updateTriggers
      }
    });
  }

  /**
   * Get color for a point based on the color scheme
   */
  private static getPointColor(
    point: EmbeddingPoint3D,
    colorBy: VisualizationConfig['colorBy'],
    colorMapping: Record<string, [number, number, number]>
  ): [number, number, number] {
    let key: string;
    
    switch (colorBy) {
      case 'robot_id':
        key = point.image_robot_id;
        break;
      case 'model_id':
        key = point.model_id;
        break;
      case 'default':
      default:
        key = 'default';
        break;
    }

    return colorMapping[key] || colorMapping.default || [128, 128, 128];
  }
}
