/**
 * Hover tooltip component for 3D points
 */

import React from 'react';
import { EmbeddingPoint3D } from '../../types/api';

interface HoverTooltipProps {
  point: EmbeddingPoint3D;
  className?: string;
}

export const HoverTooltip: React.FC<HoverTooltipProps> = ({ point, className = '' }) => {
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div
      className={`bg-black bg-opacity-80 text-white rounded-lg p-3 shadow-lg max-w-xs ${className}`}
      data-testid="hover-tooltip"
    >
      <h4 className="font-semibold mb-2" data-testid="tooltip-label">
        Point #{point.point_id}
      </h4>

      <div className="text-xs space-y-1">
        <div data-testid="tooltip-position">
          <strong>3D Position:</strong> ({point.x.toFixed(2)}, {point.y.toFixed(2)}, {point.z?.toFixed(2) || 'N/A'})
        </div>

        <div data-testid="tooltip-point-coords">
          <strong>Image Coords:</strong> ({point.point_x.toFixed(0)}, {point.point_y.toFixed(0)})
        </div>

        <div data-testid="tooltip-radius">
          <strong>Radius:</strong> {point.point_radius.toFixed(1)}px
        </div>

        <div data-testid="tooltip-image">
          <strong>Image ID:</strong> {point.image_id}
        </div>

        <div data-testid="tooltip-robot">
          <strong>Robot:</strong> {point.image_robot_id}
        </div>

        <div data-testid="tooltip-model">
          <strong>Model:</strong> {point.model_id}
        </div>

        <div data-testid="tooltip-method">
          <strong>Method:</strong> {point.method}
        </div>

        <div data-testid="tooltip-captured">
          <strong>Captured:</strong> {formatDate(point.image_captured_at)}
        </div>

        {point.image_latitude && point.image_longitude && (
          <div data-testid="tooltip-location">
            <strong>Location:</strong> {point.image_latitude.toFixed(4)}, {point.image_longitude.toFixed(4)}
          </div>
        )}
      </div>
    </div>
  );
};

export default HoverTooltip;
