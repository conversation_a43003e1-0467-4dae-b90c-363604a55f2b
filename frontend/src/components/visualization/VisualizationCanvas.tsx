/**
 * Pure rendering component for 3D visualization using Deck.gl
 */

import React, { useMemo } from 'react';
import DeckGL from '@deck.gl/react';
import { OrbitView } from '@deck.gl/core';
import { VisualizationResponse, EmbeddingPoint3D } from '../../types/api';
import { VisualizationConfig } from '../../hooks/useVisualizationConfig';
import { ViewportState } from '../../hooks/useViewport';
import { LayerFactory } from './layers/LayerFactory';

export interface VisualizationCanvasProps {
  data: VisualizationResponse;
  config: VisualizationConfig;
  viewport: ViewportState;
  onViewportChange: (viewport: ViewportState) => void;
  onHover: (point: EmbeddingPoint3D | null) => void;
  className?: string;
}

/**
 * Pure component for rendering 3D visualization
 * No side effects - only rendering logic
 */
export const VisualizationCanvas: React.FC<VisualizationCanvasProps> = ({
  data,
  config,
  viewport,
  onViewportChange,
  onHover,
  className = ''
}) => {
  // Create layers using the factory pattern
  const layers = useMemo(() => {
    if (!data || !data.points || data.points.length === 0) {
      return [];
    }

    try {
      const pointsLayer = LayerFactory.createPointsLayer(config.layerType, {
        data: data.points,
        config,
        onHover: (info) => {
          onHover(info.object || null);
        },
        updateTriggers: {
          // Additional update triggers can be added here
        }
      });

      return [pointsLayer];
    } catch (error) {
      console.error('Error creating visualization layer:', error);
      return [];
    }
  }, [data, config, onHover]);

  return (
    <div className={`${className} relative`}>
      <DeckGL
        views={new OrbitView()}
        viewState={viewport}
        onViewStateChange={({ viewState }) => onViewportChange(viewState)}
        layers={layers}
        controller={true}
        style={{ position: 'relative', width: '100%', height: '100%' }}
      />
    </div>
  );
};
