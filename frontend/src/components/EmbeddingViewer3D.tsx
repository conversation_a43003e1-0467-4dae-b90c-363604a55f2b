/**
 * 3D embedding visualization component with separated concerns
 */

import React, { useState } from 'react';
import { EmbeddingPoint3D, ImageFilters, PointFilters } from '../types/api';
import { useVisualizationData } from '../hooks/useVisualizationData';
import { useVisualizationConfig } from '../hooks/useVisualizationConfig';
import { useViewport } from '../hooks/useViewport';
import { VisualizationCanvas } from './visualization/VisualizationCanvas';
import { VisualizationControls } from './visualization/VisualizationControls';
import { VisualizationStats } from './visualization/VisualizationStats';
import { HoverTooltip } from './visualization/HoverTooltip';
import { PointsTable } from './visualization/PointsTable';
import { usePointsTable } from '../hooks/usePointsTable';

export interface EmbeddingViewer3DProps {
  method: string;
  modelId?: string;
  imageFilters?: ImageFilters;
  pointFilters?: PointFilters;
  className?: string;
}

/**
 * Main 3D embedding visualization component with separated concerns
 */
const EmbeddingViewer3D: React.FC<EmbeddingViewer3DProps> = ({
  method,
  modelId,
  imageFilters,
  pointFilters,
  className = ''
}) => {
  // Data fetching
  const { data, loading, error, refetch } = useVisualizationData({
    method,
    modelId,
    imageFilters,
    pointFilters
  });

  // Configuration management
  const { config, updateConfig } = useVisualizationConfig();

  // Viewport management
  const { viewState, setViewState } = useViewport();

  // Points table state
  const [tableState, tableActions] = usePointsTable();

  // Local state for hover interactions
  const [hoveredPoint, setHoveredPoint] = useState<EmbeddingPoint3D | null>(null);

  // Handle hover from both canvas and table
  const handlePointHover = (point: EmbeddingPoint3D | null) => {
    setHoveredPoint(point);
    tableActions.setHoveredPoint(point);
  };

  // Handle point selection from table
  const handlePointSelect = (point: EmbeddingPoint3D | null) => {
    tableActions.setSelectedPoint(point);
  };

  // Loading state
  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading visualization...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center text-red-600">
          <h3 className="text-lg font-medium mb-2">Error Loading Data</h3>
          <p className="text-sm mb-4">{error}</p>
          <button onClick={refetch} className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Debug logging
  console.log('EmbeddingViewer3D Debug:', {
    data: data,
    hasData: !!data,
    pointsLength: data?.points?.length || 0,
    loading,
    error
  });

  return (
    <div className={`${className} relative`}>
      {/* Main visualization canvas */}
      {data && (
        <VisualizationCanvas
          data={data}
          config={config}
          viewport={viewState}
          onViewportChange={setViewState}
          onHover={handlePointHover}
          className="w-full h-full"
        />
      )}

      {/* Controls overlay */}
      <div className="absolute top-4 left-4 max-w-xs">
        <VisualizationControls
          config={config}
          onConfigChange={updateConfig}
          className="mb-4"
        />

        {data && (
          <VisualizationStats
            data={data}
            className="bg-white bg-opacity-90 rounded-lg p-3 shadow-lg"
          />
        )}
      </div>

      {/* Hover tooltip */}
      {hoveredPoint && (
        <HoverTooltip
          point={hoveredPoint}
          className="absolute bottom-4 left-4"
        />
      )}

      {/* Collapsible points table - DEBUG: Always render */}
      <PointsTable
        data={data?.points || []}
        isCollapsed={tableState.isCollapsed}
        onToggleCollapse={tableActions.toggleCollapse}
        onPointHover={handlePointHover}
        onPointSelect={handlePointSelect}
      />
    </div>
  );
};

export default EmbeddingViewer3D;
