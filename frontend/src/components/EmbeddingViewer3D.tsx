/**
 * Simplified 3D embedding visualization component
 */

import React from 'react';
import { EmbeddingPoint3D } from '../types/api';
import { useVisualizationData } from '../hooks/useVisualizationData';
import { PointsTable } from './visualization/PointsTable';
import { usePointsTable } from '../hooks/usePointsTable';

export interface EmbeddingViewer3DProps {
  method: string;
  modelId?: string;
  className?: string;
}

/**
 * Simplified 3D embedding visualization component
 */
const EmbeddingViewer3D: React.FC<EmbeddingViewer3DProps> = ({
  method,
  modelId,
  className = ''
}) => {
  // Data fetching (simplified - no filters)
  const { data, loading, error, refetch } = useVisualizationData({
    method,
    modelId
  });

  // Points table state
  const [tableState, tableActions] = usePointsTable();

  // Handle hover from table (simplified)
  const handlePointHover = (point: EmbeddingPoint3D | null) => {
    tableActions.setHoveredPoint(point);
  };

  // Handle point selection from table
  const handlePointSelect = (point: EmbeddingPoint3D | null) => {
    tableActions.setSelectedPoint(point);
  };

  // Loading state
  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading visualization...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center text-red-600">
          <h3 className="text-lg font-medium mb-2">Error Loading Data</h3>
          <p className="text-sm mb-4">{error}</p>
          <button onClick={refetch} className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Debug logging
  console.log('EmbeddingViewer3D Debug:', {
    data: data,
    hasData: !!data,
    pointsLength: data?.points?.length || 0,
    loading,
    error
  });

  return (
    <div className={`${className} relative`}>
      {/* Simplified visualization - just a placeholder for now */}
      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">3D Visualization</h2>
          <p className="text-gray-600">
            {data ? `${data.points.length} points loaded` : 'Loading...'}
          </p>
        </div>
      </div>

      {/* DEBUG: Points table - should be super visible */}
      <PointsTable
        data={data?.points || []}
        isCollapsed={tableState.isCollapsed}
        onToggleCollapse={tableActions.toggleCollapse}
        onPointHover={handlePointHover}
        onPointSelect={handlePointSelect}
      />
    </div>
  );
};

export default EmbeddingViewer3D;
