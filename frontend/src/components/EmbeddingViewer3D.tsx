/**
 * Main 3D embedding visualization component using Deck.gl
 */

import React, { useState, useEffect, useCallback } from 'react';
import DeckGL from '@deck.gl/react';
import { SimpleMeshLayer } from '@deck.gl/mesh-layers';
import { LineLayer } from '@deck.gl/layers';
import { OrbitView } from '@deck.gl/core';
import { SphereGeometry } from '@luma.gl/engine';
import ApiService from '../services/api';
import { SamplingConfig } from './visualization/SamplingControls';
import {
  EmbeddingPoint3D,
  Dataset,
  VisualizationConfig,
  ViewportState,
  ColorMapping,
  DatasetInfoResponse
} from '../types/api';

interface EmbeddingViewer3DProps {
  datasetId: number;
  className?: string;
}

// Color mappings for different creature types
const CREATURE_COLORS: ColorMapping = {
  'Sparkly_Unicorn': [255, 192, 203],    // Pink
  'Dancing_Dragon': [255, 69, 0],        // Red-Orange
  'Mystical_Phoenix': [255, 140, 0],     // Dark Orange
  'Giggling_Goblin': [50, 205, 50],      // <PERSON>e <PERSON>
  'Wise_Owl': [139, 69, 19],             // Saddle Brown
  'Bouncing_Butterfly': [255, 20, 147],  // Deep Pink
  'Sleepy_Bear': [139, 90, 43],          // Saddle Brown
  'Curious_Cat': [128, 0, 128],          // Purple
  'Majestic_Eagle': [25, 25, 112],       // Midnight Blue
  'Playful_Dolphin': [0, 191, 255],      // Deep Sky Blue
  'default': [128, 128, 128]             // Gray
};

const RARITY_COLORS: ColorMapping = {
  'common': [169, 169, 169],     // Dark Gray
  'uncommon': [50, 205, 50],     // Lime Green
  'rare': [0, 191, 255],         // Deep Sky Blue
  'legendary': [255, 215, 0],    // Gold
  'default': [128, 128, 128]     // Gray
};

const EmbeddingViewer3D: React.FC<EmbeddingViewer3DProps> = ({ 
  datasetId, 
  className = '' 
}) => {
  const [embeddings, setEmbeddings] = useState<EmbeddingPoint3D[]>([]);
  const [dataset, setDataset] = useState<Dataset | null>(null);
  const [datasetInfo, setDatasetInfo] = useState<DatasetInfoResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hoveredPoint, setHoveredPoint] = useState<EmbeddingPoint3D | null>(null);
  
  const [config, setConfig] = useState<VisualizationConfig>({
    pointSize: 0.0085, // 20x smaller than previous default (0.17/20 ≈ 0.0085)
    colorBy: 'creature_type',
    showLabels: false,
    embeddingType: 'umap_3d'
  });

  const [viewState, setViewState] = useState<ViewportState>({
    target: [0, 0, 0],
    rotationOrbit: 0,
    rotationX: 30,
    zoom: 0,
    minZoom: -10,
    maxZoom: 10,
    minRotationX: -90,
    maxRotationX: 90
  });

  // Fixed sampling configuration - always use first 50k points
  const samplingConfig: SamplingConfig = {
    sampleSize: 50000,
    samplingMethod: 'first_n',
    seed: undefined
  };

  // Load dataset and embeddings
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load dataset info first to determine optimal sample size
        const [datasetResponse, infoResponse] = await Promise.all([
          ApiService.getDataset(datasetId),
          ApiService.getDatasetInfo(datasetId)
        ]);

        setDataset(datasetResponse);
        setDatasetInfo(infoResponse);

        // Load embeddings with fixed sampling configuration

        // Load embeddings with current sampling configuration
        const embeddingsResponse = await ApiService.getEmbeddings3D(datasetId, {
          embeddingType: config.embeddingType,
          sampleSize: samplingConfig.sampleSize,
          samplingMethod: samplingConfig.samplingMethod,
          seed: samplingConfig.seed
        });

        setEmbeddings(embeddingsResponse);

        // Set initial viewport based on data bounds
        if (infoResponse.embedding_types.length > 0) {
          const embeddingStats = infoResponse.embedding_types.find(
            et => et.type === config.embeddingType
          );

          if (embeddingStats?.bounds.x && embeddingStats?.bounds.y) {
            const [minX, maxX] = embeddingStats.bounds.x;
            const [minY, maxY] = embeddingStats.bounds.y;
            const centerX = (minX + maxX) / 2;
            const centerY = (minY + maxY) / 2;
            const centerZ = 0; // Default Z center
            const range = Math.max(maxX - minX, maxY - minY);

            setViewState({
              target: [centerX, centerY, centerZ],
              rotationOrbit: 0,
              rotationX: 30, // Nice 3D perspective
              zoom: Math.log2(100 / range), // Adjust zoom based on data range
              minZoom: -10,
              maxZoom: 10,
              minRotationX: -90,
              maxRotationX: 90
            });
          }
        }

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data');
        console.error('Error loading data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [datasetId, config.embeddingType]);

  // Load embeddings with current sampling configuration
  const loadEmbeddings = useCallback(async () => {
    if (!datasetInfo) return;

    try {
      setLoading(true);
      setError(null);

      const embeddingsResponse = await ApiService.getEmbeddings3D(datasetId, {
        embeddingType: config.embeddingType,
        sampleSize: samplingConfig.sampleSize,
        samplingMethod: samplingConfig.samplingMethod,
        seed: samplingConfig.seed
      });

      setEmbeddings(embeddingsResponse);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load embeddings');
      console.error('Error loading embeddings:', err);
    } finally {
      setLoading(false);
    }
  }, [datasetId, config.embeddingType, datasetInfo]);

  // Reload embeddings when sampling config changes
  useEffect(() => {
    if (datasetInfo) {
      loadEmbeddings();
    }
  }, [loadEmbeddings]);

  // No sampling controls needed - using fixed configuration

  // Get color for a point based on current color scheme
  const getPointColor = useCallback((point: EmbeddingPoint3D): [number, number, number] => {
    switch (config.colorBy) {
      case 'creature_type':
        const creatureType = point.metadata?.creature_type || 'default';
        return CREATURE_COLORS[creatureType] || CREATURE_COLORS.default;
      
      case 'rarity':
        const rarity = point.metadata?.rarity || 'default';
        return RARITY_COLORS[rarity] || RARITY_COLORS.default;
      
      case 'magic_level':
        const magicLevel = point.metadata?.magic_level || 5;
        const intensity = Math.min(255, (magicLevel / 10) * 255);
        return [intensity, 0, 255 - intensity]; // Blue to Red gradient
      
      default:
        return [100, 150, 255]; // Default blue
    }
  }, [config.colorBy]);

  // Generate grid lines for three sides (XY, XZ, YZ planes)
  const generateGridLines = useCallback(() => {
    if (!datasetInfo || !datasetInfo.embedding_types.length) return [];

    const embeddingStats = datasetInfo.embedding_types.find(
      et => et.type === config.embeddingType
    );

    if (!embeddingStats?.bounds.x || !embeddingStats?.bounds.y) return [];

    const [minX, maxX] = embeddingStats.bounds.x;
    const [minY, maxY] = embeddingStats.bounds.y;
    const [minZ, maxZ] = embeddingStats.bounds.z || [-10, 10]; // Default Z bounds

    const gridLines: Array<{
      sourcePosition: [number, number, number];
      targetPosition: [number, number, number];
      color: [number, number, number, number];
    }> = [];
    const gridSpacing = Math.max((maxX - minX) / 10, (maxY - minY) / 10, (maxZ - minZ) / 10);

    // XY plane (Z = minZ) - bottom grid
    for (let x = minX; x <= maxX; x += gridSpacing) {
      gridLines.push({
        sourcePosition: [x, minY, minZ],
        targetPosition: [x, maxY, minZ],
        color: [100, 100, 100, 80] // Gray with transparency
      });
    }
    for (let y = minY; y <= maxY; y += gridSpacing) {
      gridLines.push({
        sourcePosition: [minX, y, minZ],
        targetPosition: [maxX, y, minZ],
        color: [100, 100, 100, 80]
      });
    }

    // XZ plane (Y = minY) - back grid
    for (let x = minX; x <= maxX; x += gridSpacing) {
      gridLines.push({
        sourcePosition: [x, minY, minZ],
        targetPosition: [x, minY, maxZ],
        color: [100, 100, 100, 60]
      });
    }
    for (let z = minZ; z <= maxZ; z += gridSpacing) {
      gridLines.push({
        sourcePosition: [minX, minY, z],
        targetPosition: [maxX, minY, z],
        color: [100, 100, 100, 60]
      });
    }

    // YZ plane (X = minX) - left grid
    for (let y = minY; y <= maxY; y += gridSpacing) {
      gridLines.push({
        sourcePosition: [minX, y, minZ],
        targetPosition: [minX, y, maxZ],
        color: [100, 100, 100, 60]
      });
    }
    for (let z = minZ; z <= maxZ; z += gridSpacing) {
      gridLines.push({
        sourcePosition: [minX, minY, z],
        targetPosition: [minX, maxY, z],
        color: [100, 100, 100, 60]
      });
    }

    return gridLines;
  }, [datasetInfo, config.embeddingType]);

  // Create sphere geometry for 3D spheres
  const sphereGeometry = new SphereGeometry({
    radius: 1, // Base radius, will be scaled per instance
    nlat: 16,  // Number of latitude segments (higher = smoother)
    nlong: 16  // Number of longitude segments (higher = smoother)
  });

  // Create Deck.gl layers
  const gridLines = generateGridLines();

  const layers = [
    // Grid layer (rendered first, behind points)
    new LineLayer({
      id: 'grid-lines',
      data: gridLines,
      getSourcePosition: (d) => d.sourcePosition,
      getTargetPosition: (d) => d.targetPosition,
      getColor: (d) => d.color,
      getWidth: 1,
      pickable: false,
      updateTriggers: {
        data: [datasetInfo, config.embeddingType]
      }
    }),

    // Points layer (rendered on top of grid)
    new SimpleMeshLayer({
      id: 'embeddings-3d',
      data: embeddings,
      mesh: sphereGeometry,
      getPosition: (d: EmbeddingPoint3D) => [d.x, d.y, d.z || 0],
      getScale: (_d: EmbeddingPoint3D) => {
        const scale = config.pointSize * 30; // Scale 0-1 to 0-30
        return [scale, scale, scale]; // Uniform scaling for sphere
      },
      getColor: getPointColor,
      pickable: true,
      onHover: (info) => {
        setHoveredPoint(info.object || null);
      },
      updateTriggers: {
        getColor: [config.colorBy],
        getScale: [config.pointSize]
      }
    })
  ];

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading embeddings...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center text-red-600">
          <p className="text-lg font-semibold mb-2">Error Loading Data</p>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`relative h-full ${className}`}
      onContextMenu={(e) => e.preventDefault()}
    >
      {/* Control Instructions */}
      <div className="absolute top-4 right-4 z-10 bg-black bg-opacity-75 text-white text-xs p-2 rounded">
        <div>Left click + drag: Pan</div>
        <div>Right click + drag: Rotate</div>
        <div>Mouse wheel: Zoom</div>
      </div>

      {/* Main visualization */}
      <DeckGL
        views={new OrbitView()}
        viewState={viewState}
        onViewStateChange={({ viewState }) => setViewState(viewState)}
        layers={layers}
        controller={{
          dragMode: 'pan',        // Left click = pan, right click = rotate
          dragRotate: true,       // Enable rotation with drag
          dragPan: true,          // Enable panning with drag
          scrollZoom: true,       // Enable zoom with mouse wheel
          doubleClickZoom: true,  // Enable zoom with double click
          touchZoom: true,        // Enable touch zoom
          touchRotate: true,      // Enable touch rotation
          keyboard: true,         // Enable keyboard controls
          inertia: true          // Enable smooth inertia
        }}
        style={{ position: 'relative', width: '100%', height: '100%' }}
        onContextMenu={(e) => e.preventDefault()}
      />

      {/* Dataset info overlay */}
      <div className="absolute top-4 left-4 bg-white bg-opacity-90 rounded-lg p-4 shadow-lg max-w-sm">
        <h3 className="font-bold text-lg mb-2">{dataset?.name}</h3>
        <p className="text-sm text-gray-600 mb-2">{dataset?.description}</p>
        <div className="text-sm">
          <p><strong>Points:</strong> {embeddings.length.toLocaleString()}</p>
          <p><strong>Type:</strong> {config.embeddingType}</p>
          <p><strong>Color by:</strong> {config.colorBy.replace('_', ' ')}</p>
        </div>
      </div>

      {/* Hover tooltip */}
      {hoveredPoint && (
        <div className="absolute top-4 right-4 bg-black bg-opacity-80 text-white rounded-lg p-3 shadow-lg max-w-xs">
          <h4 className="font-semibold mb-1">{hoveredPoint.label}</h4>
          <div className="text-sm space-y-1">
            <p><strong>Position:</strong> ({hoveredPoint.x.toFixed(2)}, {hoveredPoint.y.toFixed(2)}, {hoveredPoint.z?.toFixed(2)})</p>
            {hoveredPoint.metadata?.creature_type && (
              <p><strong>Type:</strong> {hoveredPoint.metadata.creature_type.replace('_', ' ')}</p>
            )}
            {hoveredPoint.metadata?.rarity && (
              <p><strong>Rarity:</strong> {hoveredPoint.metadata.rarity}</p>
            )}
            {hoveredPoint.metadata?.magic_level && (
              <p><strong>Magic Level:</strong> {hoveredPoint.metadata.magic_level}/10</p>
            )}
          </div>
        </div>
      )}

      {/* Controls overlay */}
      <div className="absolute bottom-4 left-4 bg-white bg-opacity-90 rounded-lg p-4 shadow-lg">
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium mb-1">
              Point Size: {(config.pointSize * 100).toFixed(0)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              value={config.pointSize}
              onChange={(e) => {
                const newSize = parseFloat(e.target.value);
                console.log('Point size changed to:', newSize);
                setConfig(prev => ({ ...prev, pointSize: newSize }));
              }}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Color By</label>
            <select
              value={config.colorBy}
              onChange={(e) => setConfig(prev => ({ ...prev, colorBy: e.target.value as any }))}
              className="w-full p-1 border rounded text-sm"
            >
              <option value="creature_type">Creature Type</option>
              <option value="rarity">Rarity</option>
              <option value="magic_level">Magic Level</option>
              <option value="default">Default</option>
            </select>
          </div>

          {datasetInfo && datasetInfo.embedding_types.length > 1 && (
            <div>
              <label className="block text-sm font-medium mb-1">Embedding Type</label>
              <select
                value={config.embeddingType}
                onChange={(e) => setConfig(prev => ({ ...prev, embeddingType: e.target.value }))}
                className="w-full p-1 border rounded text-sm"
              >
                {datasetInfo.embedding_types.map(et => (
                  <option key={et.type} value={et.type}>
                    {et.type} ({et.count} points)
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Sampling controls removed - using fixed 50k first_n sampling */}
    </div>
  );
};

export default EmbeddingViewer3D;
