/**
 * Simplified 3D embedding visualization component using Deck.gl
 */

import React, { useState, useEffect } from 'react';
import DeckGL from '@deck.gl/react';
import { SimpleMeshLayer } from '@deck.gl/mesh-layers';
import { OrbitView } from '@deck.gl/core';
import { SphereGeometry } from '@luma.gl/engine';
import ApiService from '../services/api';
import {
  EmbeddingPoint3D,
  VisualizationResponse,
  ImageFilters,
  PointFilters,
  ColorMapping
} from '../types/api';

interface EmbeddingViewer3DProps {
  method: string;
  modelId?: string;
  imageFilters?: ImageFilters;
  pointFilters?: PointFilters;
  className?: string;
}

// Color mappings for different robot types
const ROBOT_COLORS: ColorMapping = {
  'robot_001': [255, 69, 0],        // Red-Orange
  'robot_002': [50, 205, 50],       // Lime Green
  'robot_003': [0, 191, 255],       // Deep Sky Blue
  'robot_004': [255, 215, 0],       // Gold
  'default': [128, 128, 128]        // Gray
};

// Color mappings for different model types
const MODEL_COLORS: ColorMapping = {
  'resnet50': [255, 69, 0],         // Red-Orange
  'vit_base': [50, 205, 50],        // Lime Green
  'clip_vit': [0, 191, 255],        // Deep Sky Blue
  'dino_v2': [255, 215, 0],         // Gold
  'default': [128, 128, 128]        // Gray
};

const EmbeddingViewer3D: React.FC<EmbeddingViewer3DProps> = ({ 
  method,
  modelId,
  imageFilters,
  pointFilters,
  className = '' 
}) => {
  const [visualizationData, setVisualizationData] = useState<VisualizationResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hoveredPoint, setHoveredPoint] = useState<EmbeddingPoint3D | null>(null);
  
  const [config, setConfig] = useState({
    pointSize: 0.5, // Increased size to make points visible
    colorBy: 'robot_id' as 'robot_id' | 'model_id' | 'default'
  });

  const [viewState, setViewState] = useState({
    target: [0, 0, 0] as [number, number, number],
    rotationOrbit: 0,
    rotationX: 30,
    zoom: 0,
    minZoom: -10,
    maxZoom: 10,
    minRotationX: -90,
    maxRotationX: 90
  });

  // Load visualization data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load visualization data with current filters
        const response = await ApiService.getVisualization3D({
          method,
          model_id: modelId,
          sample_size: 50000, // Fixed sample size for performance
          image_filters: imageFilters,
          point_filters: pointFilters
        });

        setVisualizationData(response);

        // Set initial viewport based on data bounds
        if (response.bounds) {
          const { x: [minX, maxX], y: [minY, maxY], z: [minZ, maxZ] } = response.bounds;
          const centerX = (minX + maxX) / 2;
          const centerY = (minY + maxY) / 2;
          const centerZ = (minZ + maxZ) / 2;
          const range = Math.max(maxX - minX, maxY - minY, maxZ - minZ);

          setViewState(prev => ({
            ...prev,
            target: [centerX, centerY, centerZ],
            zoom: Math.log2(100 / range) // Adjust zoom based on data range
          }));
        }

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load visualization data');
        console.error('Error loading visualization data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [method, modelId, imageFilters, pointFilters]);

  // Get color for a point based on current color scheme
  const getPointColor = (point: EmbeddingPoint3D): [number, number, number] => {
    switch (config.colorBy) {
      case 'robot_id':
        return ROBOT_COLORS[point.image_robot_id] || ROBOT_COLORS.default;
      case 'model_id':
        return MODEL_COLORS[point.model_id] || MODEL_COLORS.default;
      default:
        return [128, 128, 128]; // Gray
    }
  };

  // Create sphere geometry for points
  const sphereGeometry = new SphereGeometry({
    radius: 1,
    nlat: 8,
    nlong: 8
  });

  // Create the points layer
  const pointsLayer = visualizationData ? new SimpleMeshLayer({
    id: 'embedding-points',
    data: visualizationData.points,
    mesh: sphereGeometry,
    getPosition: (d: EmbeddingPoint3D) => [d.x, d.y, d.z || 0],
    getColor: getPointColor,
    getScale: () => config.pointSize,
    pickable: true,
    onHover: (info) => {
      setHoveredPoint(info.object || null);
    },
    updateTriggers: {
      getColor: [config.colorBy],
      getScale: [config.pointSize]
    }
  }) : null;

  const layers = pointsLayer ? [pointsLayer] : [];

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading visualization...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center text-red-600">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium mb-2">Error Loading Visualization</h3>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (!visualizationData || visualizationData.points.length === 0) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center text-gray-500">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <h3 className="text-lg font-medium mb-2">No Data Available</h3>
          <p className="text-sm">No visualization data found for the current filters.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className} relative`}>
      {/* Main visualization */}
      <DeckGL
        views={new OrbitView()}
        viewState={viewState}
        onViewStateChange={({ viewState }) => setViewState(viewState)}
        layers={layers}
        controller={true}
        style={{ position: 'relative', width: '100%', height: '100%' }}
      />

      {/* Controls overlay */}
      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-4 max-w-xs">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Visualization Controls</h3>
        
        {/* Color by selector */}
        <div className="mb-3">
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Color by:
          </label>
          <select
            value={config.colorBy}
            onChange={(e) => setConfig(prev => ({ ...prev, colorBy: e.target.value as any }))}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="robot_id">Robot ID</option>
            <option value="model_id">Model ID</option>
            <option value="default">Default</option>
          </select>
        </div>

        {/* Point size slider */}
        <div className="mb-3">
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Point size: {config.pointSize.toFixed(2)}
          </label>
          <input
            type="range"
            min="0.1"
            max="2.0"
            step="0.1"
            value={config.pointSize}
            onChange={(e) => setConfig(prev => ({ ...prev, pointSize: parseFloat(e.target.value) }))}
            className="w-full"
          />
        </div>

        {/* Stats */}
        <div className="text-xs text-gray-500">
          <div>Points: {visualizationData.total_points.toLocaleString()}</div>
          <div>Method: {visualizationData.method}</div>
          {visualizationData.model_id && <div>Model: {visualizationData.model_id}</div>}
        </div>
      </div>

      {/* Hover tooltip */}
      {hoveredPoint && (
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-75 text-white p-3 rounded-lg text-xs max-w-xs">
          <div><strong>Point ID:</strong> {hoveredPoint.point_id}</div>
          <div><strong>Image ID:</strong> {hoveredPoint.image_id}</div>
          <div><strong>Robot:</strong> {hoveredPoint.image_robot_id}</div>
          <div><strong>Model:</strong> {hoveredPoint.model_id}</div>
          <div><strong>Position:</strong> ({hoveredPoint.x.toFixed(2)}, {hoveredPoint.y.toFixed(2)}, {hoveredPoint.z?.toFixed(2) || 'N/A'})</div>
          <div><strong>Point coords:</strong> ({hoveredPoint.point_x.toFixed(0)}, {hoveredPoint.point_y.toFixed(0)})</div>
        </div>
      )}
    </div>
  );
};

export default EmbeddingViewer3D;
