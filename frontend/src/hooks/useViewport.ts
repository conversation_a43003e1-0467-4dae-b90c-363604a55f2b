/**
 * Custom hook for managing 3D viewport state
 */

import { useState, useCallback, useEffect } from 'react';
import { VisualizationResponse } from '../types/api';

export interface ViewportState {
  target: [number, number, number];
  rotationOrbit: number;
  rotationX: number;
  zoom: number;
  minZoom: number;
  maxZoom: number;
  minRotationX: number;
  maxRotationX: number;
}

export interface UseViewportResult {
  viewState: ViewportState;
  setViewState: (viewState: ViewportState) => void;
  resetViewport: () => void;
  fitToData: (data: VisualizationResponse) => void;
}

const DEFAULT_VIEWPORT: ViewportState = {
  target: [0, 0, 0],
  rotationOrbit: 0,
  rotationX: 30,
  zoom: 1,
  minZoom: -10,
  maxZoom: 10,
  minRotationX: -90,
  maxRotationX: 90
};

/**
 * Calculate optimal viewport settings based on data bounds
 */
function calculateOptimalViewport(data: VisualizationResponse): Partial<ViewportState> {
  if (!data.bounds) {
    return {};
  }

  const { x: [minX, maxX], y: [minY, maxY], z: [minZ, maxZ] } = data.bounds;
  
  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;
  const centerZ = (minZ + maxZ) / 2;
  
  const range = Math.max(maxX - minX, maxY - minY, maxZ - minZ);
  
  // Calculate zoom to fit data nicely in view
  const zoom = range > 0 ? Math.log2(50 / range) : 1;
  
  return {
    target: [centerX, centerY, centerZ],
    zoom: Math.max(-5, Math.min(5, zoom)) // Clamp zoom to reasonable range
  };
}

/**
 * Hook for managing 3D viewport state
 */
export function useViewport(
  initialViewport?: Partial<ViewportState>
): UseViewportResult {
  const [viewState, setViewState] = useState<ViewportState>({
    ...DEFAULT_VIEWPORT,
    ...initialViewport
  });

  const resetViewport = useCallback(() => {
    setViewState({ ...DEFAULT_VIEWPORT, ...initialViewport });
  }, [initialViewport]);

  const fitToData = useCallback((data: VisualizationResponse) => {
    const optimalViewport = calculateOptimalViewport(data);
    setViewState(prev => ({ ...prev, ...optimalViewport }));
  }, []);

  return {
    viewState,
    setViewState,
    resetViewport,
    fitToData
  };
}
