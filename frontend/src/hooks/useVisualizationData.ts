/**
 * Custom hook for managing 3D visualization data
 */

import { useState, useEffect, useCallback } from 'react';
import ApiService from '../services/api';
import {
  VisualizationResponse,
  ImageFilters,
  PointFilters
} from '../types/api';

export interface VisualizationOptions {
  method: string;
  modelId?: string;
  sampleSize?: number;
  imageFilters?: ImageFilters;
  pointFilters?: PointFilters;
}

export interface UseVisualizationDataResult {
  data: VisualizationResponse | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Hook for fetching 3D visualization data
 */
export function useVisualizationData(
  options: VisualizationOptions
): UseVisualizationDataResult {
  const [data, setData] = useState<VisualizationResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await ApiService.getVisualization3D({
        method: options.method,
        model_id: options.modelId,
        sample_size: options.sampleSize || 50000,
        image_filters: options.imageFilters,
        point_filters: options.pointFilters
      });

      setData(response);
      console.log('Visualization data loaded:', response.total_points, 'points');
      console.log('Method:', response.method, 'Model:', response.model_id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load visualization data');
      console.error('Error loading visualization data:', err);
    } finally {
      setLoading(false);
    }
  }, [
    options.method,
    options.modelId,
    options.sampleSize,
    options.imageFilters,
    options.pointFilters
  ]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}
