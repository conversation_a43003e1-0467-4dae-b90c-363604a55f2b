/**
 * Tests for useVisualizationData hook
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useVisualizationData } from '../useVisualizationData';
import ApiService from '../../services/api';

// Mock the API service
vi.mock('../../services/api');
const mockApiService = vi.mocked(ApiService);

const mockVisualizationResponse = {
  points: [
    {
      point_id: 1,
      x: 1.5,
      y: 2.3,
      z: -0.8,
      point_x: 100,
      point_y: 150,
      point_radius: 5,
      image_id: 1,
      image_robot_id: 'robot_001',
      image_captured_at: Date.now(),
      image_latitude: 37.7749,
      image_longitude: -122.4194,
      image_height: 1080,
      image_width: 1920,
      model_id: 'resnet50',
      method: 'umap_3d'
    }
  ],
  total_points: 1,
  method: 'umap_3d',
  model_id: 'resnet50',
  bounds: {
    x: [-2.0, 2.0],
    y: [-3.0, 3.0],
    z: [-1.0, 1.0]
  }
};

describe('useVisualizationData', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should fetch visualization data on mount', async () => {
    mockApiService.getVisualization3D.mockResolvedValueOnce(mockVisualizationResponse);

    const { result } = renderHook(() => useVisualizationData({
      method: 'umap_3d',
      modelId: 'resnet50'
    }));

    // Initially loading
    expect(result.current.loading).toBe(true);
    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe(null);

    // Wait for the API call to complete
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockVisualizationResponse);
    expect(result.current.error).toBe(null);
    expect(mockApiService.getVisualization3D).toHaveBeenCalledWith({
      method: 'umap_3d',
      model_id: 'resnet50',
      sample_size: 50000,
      image_filters: undefined,
      point_filters: undefined
    });
  });

  it('should handle API errors', async () => {
    const errorMessage = 'Failed to fetch visualization data';
    mockApiService.getVisualization3D.mockRejectedValueOnce(new Error(errorMessage));

    const { result } = renderHook(() => useVisualizationData({
      method: 'umap_3d'
    }));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe(errorMessage);
  });

  it('should refetch data when options change', async () => {
    mockApiService.getVisualization3D.mockResolvedValue(mockVisualizationResponse);

    const { result, rerender } = renderHook(
      ({ options }) => useVisualizationData(options),
      { initialProps: { options: { method: 'umap_3d' } } }
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockApiService.getVisualization3D).toHaveBeenCalledTimes(1);

    // Change options
    rerender({ options: { method: 'tsne_3d' } });

    await waitFor(() => {
      expect(mockApiService.getVisualization3D).toHaveBeenCalledTimes(2);
    });

    expect(mockApiService.getVisualization3D).toHaveBeenLastCalledWith({
      method: 'tsne_3d',
      model_id: undefined,
      sample_size: 50000,
      image_filters: undefined,
      point_filters: undefined
    });
  });

  it('should provide refetch functionality', async () => {
    mockApiService.getVisualization3D.mockResolvedValue(mockVisualizationResponse);

    const { result } = renderHook(() => useVisualizationData({
      method: 'umap_3d'
    }));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Call refetch
    result.current.refetch();

    expect(mockApiService.getVisualization3D).toHaveBeenCalledTimes(2);
  });

  it('should use custom sample size', async () => {
    mockApiService.getVisualizationData.mockResolvedValueOnce(mockVisualizationResponse);

    renderHook(() => useVisualizationData({
      method: 'umap_3d',
      sampleSize: 10000
    }));

    await waitFor(() => {
      expect(mockApiService.getVisualization3D).toHaveBeenCalledWith({
        method: 'umap_3d',
        model_id: undefined,
        sample_size: 10000,
        image_filters: undefined,
        point_filters: undefined
      });
    });
  });
});
