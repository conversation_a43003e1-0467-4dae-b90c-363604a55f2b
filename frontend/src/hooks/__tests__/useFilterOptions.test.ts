/**
 * Tests for useFilterOptions hook
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useFilterOptions } from '../useFilterOptions';
import ApiService from '../../services/api';

// Mock the API service
vi.mock('../../services/api');
const mockApiService = vi.mocked(ApiService);

describe('useFilterOptions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should fetch all filter options on mount', async () => {
    const mockRobots = ['robot_001', 'robot_002'];
    const mockModels = ['resnet50', 'vit_base'];
    const mockMethods = ['umap_3d', 'tsne_3d'];

    mockApiService.getAvailableRobots.mockResolvedValueOnce(mockRobots);
    mockApiService.getAvailableModels.mockResolvedValueOnce(mockModels);
    mockApiService.getAvailableMethods.mockResolvedValueOnce(mockMethods);

    const { result } = renderHook(() => useFilterOptions());

    // Initially loading
    expect(result.current.loading).toBe(true);
    expect(result.current.robots).toEqual([]);
    expect(result.current.models).toEqual([]);
    expect(result.current.methods).toEqual([]);
    expect(result.current.error).toBe(null);

    // Wait for the API calls to complete
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.robots).toEqual(mockRobots);
    expect(result.current.models).toEqual(mockModels);
    expect(result.current.methods).toEqual(mockMethods);
    expect(result.current.error).toBe(null);

    expect(mockApiService.getAvailableRobots).toHaveBeenCalledTimes(1);
    expect(mockApiService.getAvailableModels).toHaveBeenCalledTimes(1);
    expect(mockApiService.getAvailableMethods).toHaveBeenCalledTimes(1);
  });

  it('should handle API errors', async () => {
    const errorMessage = 'Failed to fetch filter options';
    mockApiService.getAvailableRobots.mockRejectedValueOnce(new Error(errorMessage));
    mockApiService.getAvailableModels.mockRejectedValueOnce(new Error(errorMessage));
    mockApiService.getAvailableMethods.mockRejectedValueOnce(new Error(errorMessage));

    const { result } = renderHook(() => useFilterOptions());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.robots).toEqual([]);
    expect(result.current.models).toEqual([]);
    expect(result.current.methods).toEqual([]);
    expect(result.current.error).toBe(errorMessage);
  });

  it('should handle partial API failures', async () => {
    const mockRobots = ['robot_001'];
    const errorMessage = 'Models API failed';

    mockApiService.getAvailableRobots.mockResolvedValueOnce(mockRobots);
    mockApiService.getAvailableModels.mockRejectedValueOnce(new Error(errorMessage));
    mockApiService.getAvailableMethods.mockRejectedValueOnce(new Error(errorMessage));

    const { result } = renderHook(() => useFilterOptions());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
  });

  it('should provide refetch functionality', async () => {
    const mockRobots = ['robot_001'];
    const mockModels = ['resnet50'];
    const mockMethods = ['umap_3d'];

    mockApiService.getAvailableRobots.mockResolvedValue(mockRobots);
    mockApiService.getAvailableModels.mockResolvedValue(mockModels);
    mockApiService.getAvailableMethods.mockResolvedValue(mockMethods);

    const { result } = renderHook(() => useFilterOptions());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Call refetch
    result.current.refetch();

    expect(mockApiService.getAvailableRobots).toHaveBeenCalledTimes(2);
    expect(mockApiService.getAvailableModels).toHaveBeenCalledTimes(2);
    expect(mockApiService.getAvailableMethods).toHaveBeenCalledTimes(2);
  });

  it('should reset error state on refetch', async () => {
    // First call fails
    mockApiService.getAvailableRobots.mockRejectedValueOnce(new Error('Network error'));
    mockApiService.getAvailableModels.mockRejectedValueOnce(new Error('Network error'));
    mockApiService.getAvailableMethods.mockRejectedValueOnce(new Error('Network error'));

    const { result } = renderHook(() => useFilterOptions());

    await waitFor(() => {
      expect(result.current.error).toBe('Network error');
    });

    // Second call succeeds
    const mockRobots = ['robot_001'];
    const mockModels = ['resnet50'];
    const mockMethods = ['umap_3d'];

    mockApiService.getAvailableRobots.mockResolvedValueOnce(mockRobots);
    mockApiService.getAvailableModels.mockResolvedValueOnce(mockModels);
    mockApiService.getAvailableMethods.mockResolvedValueOnce(mockMethods);

    result.current.refetch();

    await waitFor(() => {
      expect(result.current.error).toBe(null);
      expect(result.current.robots).toEqual(mockRobots);
      expect(result.current.models).toEqual(mockModels);
      expect(result.current.methods).toEqual(mockMethods);
    });
  });
});
