/**
 * Tests for useVisualizationConfig hook
 */

import { describe, it, expect } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useVisualizationConfig } from '../useVisualizationConfig';

describe('useVisualizationConfig', () => {
  it('should initialize with default config', () => {
    const { result } = renderHook(() => useVisualizationConfig());

    expect(result.current.config).toEqual({
      pointSize: 0.5,
      colorBy: 'robot_id',
      layerType: 'scatterplot',
      showLabels: false
    });
  });

  it('should initialize with custom initial config', () => {
    const initialConfig = {
      pointSize: 1.0,
      colorBy: 'model_id' as const
    };

    const { result } = renderHook(() => useVisualizationConfig(initialConfig));

    expect(result.current.config).toEqual({
      pointSize: 1.0,
      colorBy: 'model_id',
      layerType: 'scatterplot',
      showLabels: false
    });
  });

  it('should update config with partial updates', () => {
    const { result } = renderHook(() => useVisualizationConfig());

    act(() => {
      result.current.updateConfig({ pointSize: 1.5 });
    });

    expect(result.current.config).toEqual({
      pointSize: 1.5,
      colorBy: 'robot_id',
      layerType: 'scatterplot',
      showLabels: false
    });
  });

  it('should update multiple config properties at once', () => {
    const { result } = renderHook(() => useVisualizationConfig());

    act(() => {
      result.current.updateConfig({
        pointSize: 2.0,
        colorBy: 'model_id',
        layerType: 'mesh'
      });
    });

    expect(result.current.config).toEqual({
      pointSize: 2.0,
      colorBy: 'model_id',
      layerType: 'mesh',
      showLabels: false
    });
  });

  it('should reset config to defaults', () => {
    const { result } = renderHook(() => useVisualizationConfig());

    // First, change the config
    act(() => {
      result.current.updateConfig({
        pointSize: 2.0,
        colorBy: 'model_id',
        showLabels: true
      });
    });

    expect(result.current.config.pointSize).toBe(2.0);

    // Then reset
    act(() => {
      result.current.resetConfig();
    });

    expect(result.current.config).toEqual({
      pointSize: 0.5,
      colorBy: 'robot_id',
      layerType: 'scatterplot',
      showLabels: false
    });
  });

  it('should reset config to custom initial config', () => {
    const initialConfig = {
      pointSize: 1.0,
      colorBy: 'model_id' as const
    };

    const { result } = renderHook(() => useVisualizationConfig(initialConfig));

    // Change the config
    act(() => {
      result.current.updateConfig({
        pointSize: 2.0,
        showLabels: true
      });
    });

    // Reset should go back to initial config, not defaults
    act(() => {
      result.current.resetConfig();
    });

    expect(result.current.config).toEqual({
      pointSize: 1.0,
      colorBy: 'model_id',
      layerType: 'scatterplot',
      showLabels: false
    });
  });

  it('should handle all valid color schemes', () => {
    const { result } = renderHook(() => useVisualizationConfig());

    act(() => {
      result.current.updateConfig({ colorBy: 'robot_id' });
    });
    expect(result.current.config.colorBy).toBe('robot_id');

    act(() => {
      result.current.updateConfig({ colorBy: 'model_id' });
    });
    expect(result.current.config.colorBy).toBe('model_id');

    act(() => {
      result.current.updateConfig({ colorBy: 'default' });
    });
    expect(result.current.config.colorBy).toBe('default');
  });

  it('should handle all valid layer types', () => {
    const { result } = renderHook(() => useVisualizationConfig());

    act(() => {
      result.current.updateConfig({ layerType: 'scatterplot' });
    });
    expect(result.current.config.layerType).toBe('scatterplot');

    act(() => {
      result.current.updateConfig({ layerType: 'mesh' });
    });
    expect(result.current.config.layerType).toBe('mesh');
  });

  it('should preserve other config values when updating', () => {
    const { result } = renderHook(() => useVisualizationConfig());

    // Set initial state
    act(() => {
      result.current.updateConfig({
        pointSize: 1.0,
        colorBy: 'model_id',
        showLabels: true
      });
    });

    // Update only one property
    act(() => {
      result.current.updateConfig({ pointSize: 1.5 });
    });

    // Other properties should remain unchanged
    expect(result.current.config).toEqual({
      pointSize: 1.5,
      colorBy: 'model_id',
      layerType: 'scatterplot',
      showLabels: true
    });
  });
});
