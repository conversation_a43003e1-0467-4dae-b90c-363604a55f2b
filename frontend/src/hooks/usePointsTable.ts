/**
 * Hook for managing points table state and interactions
 */

import { useState, useCallback } from 'react';
import { EmbeddingPoint3D } from '../types/api';

export interface PointsTableState {
  isCollapsed: boolean;
  selectedPoint: EmbeddingPoint3D | null;
  hoveredPoint: EmbeddingPoint3D | null;
}

export interface PointsTableActions {
  toggleCollapse: () => void;
  setSelectedPoint: (point: EmbeddingPoint3D | null) => void;
  setHoveredPoint: (point: EmbeddingPoint3D | null) => void;
  collapseTable: () => void;
  expandTable: () => void;
}

export const usePointsTable = (
  initialCollapsed: boolean = true
): [PointsTableState, PointsTableActions] => {
  const [isCollapsed, setIsCollapsed] = useState(initialCollapsed);
  const [selectedPoint, setSelectedPoint] = useState<EmbeddingPoint3D | null>(null);
  const [hoveredPoint, setHoveredPoint] = useState<EmbeddingPoint3D | null>(null);

  const toggleCollapse = useCallback(() => {
    setIsCollapsed((prev) => !prev);
  }, []);

  const collapseTable = useCallback(() => {
    setIsCollapsed(true);
  }, []);

  const expandTable = useCallback(() => {
    setIsCollapsed(false);
  }, []);

  const handleSetSelectedPoint = useCallback((point: EmbeddingPoint3D | null) => {
    setSelectedPoint(point);
  }, []);

  const handleSetHoveredPoint = useCallback((point: EmbeddingPoint3D | null) => {
    setHoveredPoint(point);
  }, []);

  const state: PointsTableState = {
    isCollapsed,
    selectedPoint,
    hoveredPoint,
  };

  const actions: PointsTableActions = {
    toggleCollapse,
    setSelectedPoint: handleSetSelectedPoint,
    setHoveredPoint: handleSetHoveredPoint,
    collapseTable,
    expandTable,
  };

  return [state, actions];
};
