/**
 * Custom hook for managing visualization configuration state
 */

import { useState, useCallback } from 'react';

export type ColorScheme = 'robot_id' | 'model_id' | 'default';
export type LayerType = 'scatterplot' | 'mesh';

export interface VisualizationConfig {
  pointSize: number;
  colorBy: ColorScheme;
  layerType: LayerType;
  showLabels: boolean;
}

export interface UseVisualizationConfigResult {
  config: VisualizationConfig;
  updateConfig: (updates: Partial<VisualizationConfig>) => void;
  resetConfig: () => void;
}

const DEFAULT_CONFIG: VisualizationConfig = {
  pointSize: 0.5,
  colorBy: 'robot_id',
  layerType: 'scatterplot',
  showLabels: false
};

/**
 * Hook for managing visualization configuration
 */
export function useVisualizationConfig(
  initialConfig?: Partial<VisualizationConfig>
): UseVisualizationConfigResult {
  const [config, setConfig] = useState<VisualizationConfig>({
    ...DEFAULT_CONFIG,
    ...initialConfig
  });

  const updateConfig = useCallback((updates: Partial<VisualizationConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  }, []);

  const resetConfig = useCallback(() => {
    setConfig({ ...DEFAULT_CONFIG, ...initialConfig });
  }, [initialConfig]);

  return {
    config,
    updateConfig,
    resetConfig
  };
}
