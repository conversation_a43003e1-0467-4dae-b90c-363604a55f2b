/**
 * Custom hook for managing filter options (robots, models, methods)
 */

import { useState, useEffect } from 'react';
import ApiService from '../services/api';

export interface UseFilterOptionsResult {
  robots: string[];
  models: string[];
  methods: string[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Hook for fetching available filter options
 */
export function useFilterOptions(): UseFilterOptionsResult {
  const [robots, setRobots] = useState<string[]>([]);
  const [models, setModels] = useState<string[]>([]);
  const [methods, setMethods] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFilterOptions = async () => {
    try {
      setLoading(true);
      setError(null);

      const [robotsResponse, modelsResponse, methodsResponse] = await Promise.all([
        ApiService.getAvailableRobots(),
        ApiService.getAvailableModels(),
        ApiService.getAvailableMethods()
      ]);

      setRobots(robotsResponse);
      setModels(modelsResponse);
      setMethods(methodsResponse);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load filter options');
      console.error('Error loading filter options:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFilterOptions();
  }, []);

  return {
    robots,
    models,
    methods,
    loading,
    error,
    refetch: fetchFilterOptions
  };
}
