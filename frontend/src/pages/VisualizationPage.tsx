/**
 * Visualization page for 3D embedding visualization
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import EmbeddingViewer3D from '../components/EmbeddingViewer3D_refactored';
import { useFilterOptions } from '../hooks/useFilterOptions';
import { ImageFilters, PointFilters } from '../types/api';

const VisualizationPage: React.FC = () => {
  const navigate = useNavigate();

  // Use the new hook for filter options
  const { robots, models, methods, loading, error } = useFilterOptions();

  // Filter states
  const [selectedMethod, setSelectedMethod] = useState<string>('umap_3d');
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [selectedRobot, setSelectedRobot] = useState<string>('');

  const handleBackToImages = () => {
    navigate('/images');
  };

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading visualization options...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h2 className="text-xl font-semibold mb-2">Error Loading Visualization</h2>
            <p className="text-gray-600 mb-4">{error}</p>
          </div>
          <button
            onClick={handleBackToImages}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Back to Images
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBackToImages}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
            aria-label="Back to images"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <div>
            <h1 className="font-semibold text-gray-900">3D Visualization</h1>
            <p className="text-sm text-gray-500">
              Interactive embedding visualization
            </p>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="flex items-center space-x-4">
          {/* Method Selection */}
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-600">Method:</label>
            <select
              value={selectedMethod}
              onChange={(e) => setSelectedMethod(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              {methods.map(method => (
                <option key={method} value={method}>{method}</option>
              ))}
            </select>
          </div>

          {/* Model Selection */}
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-600">Model:</label>
            <select
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All models</option>
              {models.map(model => (
                <option key={model} value={model}>{model}</option>
              ))}
            </select>
          </div>

          {/* Robot Selection */}
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-600">Robot:</label>
            <select
              value={selectedRobot}
              onChange={(e) => setSelectedRobot(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All robots</option>
              {robots.map(robot => (
                <option key={robot} value={robot}>{robot}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Visualization area */}
      <div className="flex-1 relative">
        <EmbeddingViewer3D
          method={selectedMethod}
          modelId={selectedModel || undefined}
          imageFilters={selectedRobot ? { robot_id: [selectedRobot] } : undefined}
          className="w-full h-full"
        />
      </div>
    </div>
  );
};

export default VisualizationPage;
