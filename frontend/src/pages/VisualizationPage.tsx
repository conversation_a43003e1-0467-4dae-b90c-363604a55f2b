/**
 * Visualization page for 3D embedding visualization
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ApiService from '../services/api';
import EmbeddingViewer3D from '../components/EmbeddingViewer3D';
import { Dataset } from '../types/api';

const VisualizationPage: React.FC = () => {
  const { datasetId } = useParams<{ datasetId: string }>();
  const navigate = useNavigate();
  const [dataset, setDataset] = useState<Dataset | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!datasetId) {
      navigate('/datasets');
      return;
    }

    loadDataset();
  }, [datasetId, navigate]);

  const loadDataset = async () => {
    if (!datasetId) return;

    try {
      setLoading(true);
      setError(null);
      const datasetData = await ApiService.getDataset(parseInt(datasetId));
      setDataset(datasetData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dataset');
      console.error('Error loading dataset:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToDatasets = () => {
    navigate('/datasets');
  };

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dataset...</p>
        </div>
      </div>
    );
  }

  if (error || !dataset) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h2 className="text-xl font-semibold mb-2">Dataset Not Found</h2>
            <p className="text-gray-600 mb-4">{error || 'The requested dataset could not be loaded.'}</p>
          </div>
          <button
            onClick={handleBackToDatasets}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Back to Datasets
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBackToDatasets}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
            aria-label="Back to datasets"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <div>
            <h1 className="font-semibold text-gray-900">{dataset.name}</h1>
            <p className="text-sm text-gray-500">
              {dataset.total_points.toLocaleString()} points
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Status:
            <span className={`ml-1 px-2 py-1 rounded-full text-xs ${
              dataset.status === 'ready'
                ? 'bg-green-100 text-green-800'
                : dataset.status === 'processing'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-gray-100 text-gray-800'
            }`}>
              {dataset.status}
            </span>
          </div>
          
          <div className="text-sm text-gray-500">
            ID: {dataset.id}
          </div>
        </div>
      </div>

      {/* Visualization area */}
      <div className="flex-1 relative">
        {dataset.status === 'ready' ? (
          <EmbeddingViewer3D
            datasetId={dataset.id}
            className="w-full h-full"
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-medium mb-2">Dataset Not Ready</h3>
              <p className="text-sm">
                This dataset is currently {dataset.status}. 
                {dataset.status === 'processing' && ' Please wait for processing to complete.'}
                {dataset.status === 'error' && dataset.error_message && (
                  <span className="block mt-2 text-red-600">
                    Error: {dataset.error_message}
                  </span>
                )}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VisualizationPage;
