/**
 * Simplified visualization page for 3D embedding visualization
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import EmbeddingViewer3D from '../components/EmbeddingViewer3D';

const VisualizationPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBackToImages = () => {
    navigate('/images');
  };

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleBackToImages}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Images
            </button>
            <div className="h-6 w-px bg-gray-300"></div>
            <h1 className="text-xl font-semibold text-gray-900">3D Visualization</h1>
          </div>
        </div>
      </div>

      {/* Visualization area */}
      <div className="flex-1 relative">
        <EmbeddingViewer3D
          method="umap_3d"
          modelId={undefined}
          className="w-full h-full"
        />
      </div>
    </div>
  );
};

export default VisualizationPage;
