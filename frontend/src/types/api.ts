/**
 * TypeScript types for API responses and data structures
 */

export interface Image {
  id: number;
  user_id: number;
  captured_at: number;
  latitude?: number;
  longitude?: number;
  height: number;
  width: number;
  robot_id: string;
  file_path?: string;
  file_size?: number;
  created_at: number;
  updated_at: number;
  total_points?: number;
}

export interface ImageListResponse {
  images: Image[];
  total: number;
  skip: number;
  limit: number;
}

export interface PointResponse {
  id: number;
  image_id: number;
  x: number;
  y: number;
  radius: number;
  created_at: number;
  updated_at: number;
  // Optional image metadata
  image_captured_at?: number;
  image_latitude?: number;
  image_longitude?: number;
  image_height?: number;
  image_width?: number;
  image_robot_id?: string;
}

export interface EmbeddingPoint3D {
  id: number; // reduced_embedding id
  x: number;
  y: number;
  z?: number;
  method: string;
  model_id: string;
  // Point data
  point_id: number;
  point_x: number;
  point_y: number;
  point_radius: number;
  // Image metadata for filtering/coloring
  image_id: number;
  image_captured_at: number;
  image_latitude?: number;
  image_longitude?: number;
  image_robot_id: string;
}

export interface VisualizationResponse {
  method: string;
  model_id?: string;
  total_points: number;
  points: EmbeddingPoint3D[];
  bounds: {
    x: [number, number];
    y: [number, number];
    z: [number, number];
  };
}

export interface ImageFilters {
  captured_after?: number;
  captured_before?: number;
  lat_min?: number;
  lat_max?: number;
  lng_min?: number;
  lng_max?: number;
  robot_id?: string[];
  height_min?: number;
  height_max?: number;
  width_min?: number;
  width_max?: number;
}

export interface PointFilters {
  x_min?: number;
  x_max?: number;
  y_min?: number;
  y_max?: number;
  radius_min?: number;
  radius_max?: number;
}

export interface ApiError {
  error: string;
  message: string;
  details?: Record<string, any>;
}

// Visualization-specific types
export interface VisualizationConfig {
  pointSize: number;
  colorBy: 'robot_id' | 'model_id' | 'image_time' | 'default';
  showLabels: boolean;
  method: string;
  model_id?: string;
}

export interface ViewportState {
  target: [number, number, number];
  rotationOrbit: number;
  rotationX: number;
  zoom: number;
  minZoom?: number;
  maxZoom?: number;
  minRotationX?: number;
  maxRotationX?: number;
}

export interface ColorMapping {
  [key: string]: [number, number, number]; // RGB values
}
