{"version": "0.34.6", "results": [[":src/utils/__tests__/viewport.test.ts", {"duration": 50, "failed": false}], [":src/utils/__tests__/colorMappings.test.ts", {"duration": 100, "failed": false}], [":src/utils/__tests__/formatters.test.ts", {"duration": 210, "failed": false}], [":src/components/visualization/layers/__tests__/LayerFactory.test.ts", {"duration": 21, "failed": false}], [":src/hooks/__tests__/useVisualizationConfig.test.ts", {"duration": 113, "failed": false}], [":src/components/ui/__tests__/LoadingSpinner.test.tsx", {"duration": 632, "failed": false}], [":src/components/ui/__tests__/ErrorMessage.test.tsx", {"duration": 725, "failed": false}], [":src/components/visualization/__tests__/VisualizationCanvas.test.tsx", {"duration": 118, "failed": false}], [":src/components/visualization/__tests__/VisualizationControls.test.tsx", {"duration": 204, "failed": false}], [":src/services/__tests__/api.test.ts", {"duration": 248, "failed": true}], [":src/components/ui/__tests__/StatusBadge.test.tsx", {"duration": 556, "failed": false}], [":src/components/__tests__/integration.test.tsx", {"duration": 805, "failed": false}], [":src/hooks/__tests__/useFilterOptions.test.ts", {"duration": 385, "failed": false}], [":src/hooks/__tests__/useVisualizationData.test.ts", {"duration": 283, "failed": false}], [":src/hooks/__tests__/useDatasets.test.ts", {"duration": 241, "failed": true}], [":src/hooks/__tests__/useEmbeddings.test.ts", {"duration": 262, "failed": true}]]}