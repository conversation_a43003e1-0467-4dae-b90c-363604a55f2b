{"version": 3, "file": "private-ssr-support.js", "sources": ["../../src/private-ssr-support.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {\n  Directive,\n  PartInfo,\n  DirectiveClass,\n  DirectiveResult,\n} from './directive.js';\nimport {\n  _$LH as p,\n  AttributePart,\n  noChange,\n  Part,\n  Disconnectable,\n} from './lit-html.js';\n\nimport type {\n  PropertyPart,\n  ChildPart,\n  BooleanAttributePart,\n  EventPart,\n  ElementPart,\n  TemplateInstance,\n} from './lit-html.js';\n\n// Contains either the minified or unminified `_$resolve` Directive method name.\nlet resolveMethodName: Extract<keyof Directive, '_$resolve'> | null = null;\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports mangled in the\n * client side code, we export a _$LH object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n * @private\n */\nexport const _$LH = {\n  boundAttributeSuffix: p._boundAttributeSuffix,\n  marker: p._marker,\n  markerMatch: p._markerMatch,\n  HTML_RESULT: p._HTML_RESULT,\n  getTemplateHtml: p._getTemplateHtml,\n  overrideDirectiveResolve: (\n    directiveClass: new (part: PartInfo) => Directive & {render(): unknown},\n    resolveOverrideFn: (directive: Directive, values: unknown[]) => unknown\n  ) =>\n    class extends directiveClass {\n      override _$resolve(\n        this: Directive,\n        _part: Part,\n        values: unknown[]\n      ): unknown {\n        return resolveOverrideFn(this, values);\n      }\n    },\n  patchDirectiveResolve: (\n    directiveClass: typeof Directive,\n    resolveOverrideFn: (\n      this: Directive,\n      _part: Part,\n      values: unknown[]\n    ) => unknown\n  ) => {\n    if (directiveClass.prototype._$resolve !== resolveOverrideFn) {\n      resolveMethodName ??= directiveClass.prototype._$resolve\n        .name as NonNullable<typeof resolveMethodName>;\n      for (\n        let proto = directiveClass.prototype;\n        proto !== Object.prototype;\n        proto = Object.getPrototypeOf(proto)\n      ) {\n        if (proto.hasOwnProperty(resolveMethodName)) {\n          proto[resolveMethodName] = resolveOverrideFn;\n          return;\n        }\n      }\n      // Nothing was patched which indicates an error. The most likely error is\n      // that somehow both minified and unminified lit code passed through this\n      // codepath. This is possible as lit-labs/ssr contains its own lit-html\n      // module as a dependency for server rendering client Lit code. If a\n      // client contains multiple duplicate Lit modules with minified and\n      // unminified exports, we currently cannot handle both.\n      throw new Error(\n        `Internal error: It is possible that both dev mode and production mode` +\n          ` Lit was mixed together during SSR. Please comment on the issue: ` +\n          `https://github.com/lit/lit/issues/4527`\n      );\n    }\n  },\n  setDirectiveClass(value: DirectiveResult, directiveClass: DirectiveClass) {\n    // This property needs to remain unminified.\n    value['_$litDirective$'] = directiveClass;\n  },\n  getAttributePartCommittedValue: (\n    part: AttributePart,\n    value: unknown,\n    index: number | undefined\n  ) => {\n    // Use the part setter to resolve directives/concatenate multiple parts\n    // into a final value (captured by passing in a commitValue override)\n    let committedValue: unknown = noChange;\n    // Note that _commitValue need not be in `stableProperties` because this\n    // method is only run on `AttributePart`s created by lit-ssr using the same\n    // version of the library as this file\n    part._commitValue = (value: unknown) => (committedValue = value);\n    part._$setValue(value, part, index);\n    return committedValue;\n  },\n  connectedDisconnectable: (props?: object): Disconnectable => ({\n    ...props,\n    _$isConnected: true,\n  }),\n  resolveDirective: p._resolveDirective,\n  AttributePart: p._AttributePart,\n  PropertyPart: p._PropertyPart as typeof PropertyPart,\n  BooleanAttributePart: p._BooleanAttributePart as typeof BooleanAttributePart,\n  EventPart: p._EventPart as typeof EventPart,\n  ElementPart: p._ElementPart as typeof ElementPart,\n  TemplateInstance: p._TemplateInstance as typeof TemplateInstance,\n  isIterable: p._isIterable,\n  ChildPart: p._ChildPart as typeof ChildPart,\n};\n"], "names": ["p"], "mappings": ";;AAAA;;;;AAIG;AAyBH;AACA,IAAI,iBAAiB,GAAiD,IAAI,CAAC;AAE3E;;;;;;;;;;AAUG;AACU,MAAA,IAAI,GAAG;IAClB,oBAAoB,EAAEA,MAAC,CAAC,qBAAqB;IAC7C,MAAM,EAAEA,MAAC,CAAC,OAAO;IACjB,WAAW,EAAEA,MAAC,CAAC,YAAY;IAC3B,WAAW,EAAEA,MAAC,CAAC,YAAY;IAC3B,eAAe,EAAEA,MAAC,CAAC,gBAAgB;IACnC,wBAAwB,EAAE,CACxB,cAAuE,EACvE,iBAAuE,KAEvE,cAAc,cAAc,CAAA;QACjB,SAAS,CAEhB,KAAW,EACX,MAAiB,EAAA;AAEjB,YAAA,OAAO,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACxC;AACF,KAAA;AACH,IAAA,qBAAqB,EAAE,CACrB,cAAgC,EAChC,iBAIY,KACV;QACF,IAAI,cAAc,CAAC,SAAS,CAAC,SAAS,KAAK,iBAAiB,EAAE;AAC5D,YAAA,iBAAiB,KAAK,cAAc,CAAC,SAAS,CAAC,SAAS;AACrD,iBAAA,IAA6C,CAAC;YACjD,KACE,IAAI,KAAK,GAAG,cAAc,CAAC,SAAS,EACpC,KAAK,KAAK,MAAM,CAAC,SAAS,EAC1B,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EACpC;AACA,gBAAA,IAAI,KAAK,CAAC,cAAc,CAAC,iBAAiB,CAAC,EAAE;AAC3C,oBAAA,KAAK,CAAC,iBAAiB,CAAC,GAAG,iBAAiB,CAAC;oBAC7C,OAAO;iBACR;aACF;;;;;;;YAOD,MAAM,IAAI,KAAK,CACb,CAAuE,qEAAA,CAAA;gBACrE,CAAmE,iEAAA,CAAA;AACnE,gBAAA,CAAA,sCAAA,CAAwC,CAC3C,CAAC;SACH;KACF;IACD,iBAAiB,CAAC,KAAsB,EAAE,cAA8B,EAAA;;AAEtE,QAAA,KAAK,CAAC,iBAAiB,CAAC,GAAG,cAAc,CAAC;KAC3C;IACD,8BAA8B,EAAE,CAC9B,IAAmB,EACnB,KAAc,EACd,KAAyB,KACvB;;;QAGF,IAAI,cAAc,GAAY,QAAQ,CAAC;;;;AAIvC,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,KAAc,MAAM,cAAc,GAAG,KAAK,CAAC,CAAC;QACjE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACpC,QAAA,OAAO,cAAc,CAAC;KACvB;AACD,IAAA,uBAAuB,EAAE,CAAC,KAAc,MAAsB;AAC5D,QAAA,GAAG,KAAK;AACR,QAAA,aAAa,EAAE,IAAI;KACpB,CAAC;IACF,gBAAgB,EAAEA,MAAC,CAAC,iBAAiB;IACrC,aAAa,EAAEA,MAAC,CAAC,cAAc;IAC/B,YAAY,EAAEA,MAAC,CAAC,aAAoC;IACpD,oBAAoB,EAAEA,MAAC,CAAC,qBAAoD;IAC5E,SAAS,EAAEA,MAAC,CAAC,UAA8B;IAC3C,WAAW,EAAEA,MAAC,CAAC,YAAkC;IACjD,gBAAgB,EAAEA,MAAC,CAAC,iBAA4C;IAChE,UAAU,EAAEA,MAAC,CAAC,WAAW;IACzB,SAAS,EAAEA,MAAC,CAAC,UAA8B;;;;;"}